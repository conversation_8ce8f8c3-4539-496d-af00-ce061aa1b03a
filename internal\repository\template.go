package repository

import (
	"time"

	"gorm.io/gorm"
)

// Template 模板数据模型
type Template struct {
	ID              uint      `gorm:"primaryKey"`
	Name            string    `gorm:"not null"`
	FilePath        string    `gorm:"not null"`
	SignatureX      float64   `gorm:"not null"`
	SignatureY      float64   `gorm:"not null"`
	SignatureWidth  float64   `gorm:"default:100"`
	SignatureHeight float64   `gorm:"default:50"`
	QRCodePath      string
	CreatedAt       time.Time
	UpdatedAt       time.Time
}

// TemplateRepository 模板仓储接口
type TemplateRepository interface {
	Create(template *Template) error
	GetByID(id uint) (*Template, error)
	GetAll() ([]*Template, error)
	Update(template *Template) error
	Delete(id uint) error
}

// templateRepository 模板仓储实现
type templateRepository struct {
	db *gorm.DB
}

// NewTemplateRepository 创建模板仓储实例
func NewTemplateRepository(db *gorm.DB) TemplateRepository {
	return &templateRepository{db: db}
}

// Create 创建模板
func (r *templateRepository) Create(template *Template) error {
	return r.db.Create(template).Error
}

// GetByID 根据ID获取模板
func (r *templateRepository) GetByID(id uint) (*Template, error) {
	var template Template
	err := r.db.First(&template, id).Error
	if err != nil {
		return nil, err
	}
	return &template, nil
}

// GetAll 获取所有模板
func (r *templateRepository) GetAll() ([]*Template, error) {
	var templates []*Template
	err := r.db.Find(&templates).Error
	return templates, err
}

// Update 更新模板
func (r *templateRepository) Update(template *Template) error {
	return r.db.Save(template).Error
}

// Delete 删除模板
func (r *templateRepository) Delete(id uint) error {
	return r.db.Delete(&Template{}, id).Error
}
