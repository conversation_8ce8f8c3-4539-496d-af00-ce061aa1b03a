<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理后台 - 电子签名系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
            color: #333;
            line-height: 1.6;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem 2rem;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .header h1 {
            font-size: 1.5rem;
            font-weight: 600;
        }

        .container {
            max-width: 1200px;
            margin: 2rem auto;
            padding: 0 1rem;
        }

        .dashboard {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .card {
            background: white;
            border-radius: 12px;
            padding: 1.5rem;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
            border: 1px solid #e1e5e9;
            transition: all 0.3s ease;
        }

        .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }

        .stat-card {
            text-align: center;
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }

        .stat-label {
            color: #666;
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .stat-templates .stat-number { color: #667eea; }
        .stat-signatures .stat-number { color: #4CAF50; }
        .stat-today .stat-number { color: #FF9800; }
        .stat-active .stat-number { color: #9C27B0; }

        .actions {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .action-card {
            background: white;
            border-radius: 12px;
            padding: 1.5rem;
            text-align: center;
            text-decoration: none;
            color: #333;
            border: 2px solid transparent;
            transition: all 0.3s ease;
        }

        .action-card:hover {
            border-color: #667eea;
            transform: translateY(-2px);
            text-decoration: none;
            color: #667eea;
        }

        .action-icon {
            font-size: 2rem;
            margin-bottom: 0.5rem;
            display: block;
        }

        .action-title {
            font-weight: 600;
            margin-bottom: 0.25rem;
        }

        .action-desc {
            font-size: 0.85rem;
            color: #666;
        }

        .recent-section {
            background: white;
            border-radius: 12px;
            padding: 1.5rem;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
        }

        .section-title {
            font-size: 1.25rem;
            font-weight: 600;
            margin-bottom: 1rem;
            color: #333;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
        }

        .table th,
        .table td {
            padding: 0.75rem;
            text-align: left;
            border-bottom: 1px solid #e1e5e9;
        }

        .table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #555;
            font-size: 0.85rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .table tr:hover {
            background: #f8f9fa;
        }

        .status {
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            font-size: 0.75rem;
            font-weight: 500;
            text-transform: uppercase;
        }

        .status-active {
            background: #d4edda;
            color: #155724;
        }

        .status-inactive {
            background: #f8d7da;
            color: #721c24;
        }

        .btn {
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 6px;
            font-size: 0.85rem;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: #667eea;
            color: white;
        }

        .btn-primary:hover {
            background: #5a67d8;
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-secondary:hover {
            background: #5a6268;
        }

        .btn-sm {
            padding: 0.25rem 0.5rem;
            font-size: 0.75rem;
        }

        .loading {
            text-align: center;
            padding: 2rem;
            color: #666;
        }

        .loading-spinner {
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin: 0 auto 1rem;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .empty-state {
            text-align: center;
            padding: 3rem 1rem;
            color: #666;
        }

        .empty-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
            opacity: 0.5;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .header {
                padding: 1rem;
            }
            
            .container {
                margin: 1rem auto;
                padding: 0 0.5rem;
            }
            
            .dashboard {
                grid-template-columns: repeat(2, 1fr);
                gap: 1rem;
            }
            
            .actions {
                grid-template-columns: 1fr;
            }
            
            .table {
                font-size: 0.85rem;
            }
            
            .table th,
            .table td {
                padding: 0.5rem 0.25rem;
            }
        }

        @media (max-width: 480px) {
            .dashboard {
                grid-template-columns: 1fr;
            }
            
            .stat-number {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>📋 电子签名系统 - 管理后台</h1>
    </div>

    <div class="container">
        <!-- 统计卡片 -->
        <div class="dashboard">
            <div class="card stat-card stat-templates">
                <div class="stat-number" id="totalTemplates">-</div>
                <div class="stat-label">模板总数</div>
            </div>
            <div class="card stat-card stat-signatures">
                <div class="stat-number" id="totalSignatures">-</div>
                <div class="stat-label">签名总数</div>
            </div>
            <div class="card stat-card stat-today">
                <div class="stat-number" id="todaySignatures">-</div>
                <div class="stat-label">今日签名</div>
            </div>
            <div class="card stat-card stat-active">
                <div class="stat-number" id="activeTemplates">-</div>
                <div class="stat-label">活跃模板</div>
            </div>
        </div>

        <!-- 快捷操作 -->
        <div class="actions">
            <a href="/admin/templates" class="action-card">
                <span class="action-icon">📄</span>
                <div class="action-title">模板管理</div>
                <div class="action-desc">创建、编辑和管理PDF模板</div>
            </a>
            <a href="/admin/signatures" class="action-card">
                <span class="action-icon">✍️</span>
                <div class="action-title">签名记录</div>
                <div class="action-desc">查看和管理所有签名记录</div>
            </a>
            <a href="/admin/settings" class="action-card">
                <span class="action-icon">⚙️</span>
                <div class="action-title">系统设置</div>
                <div class="action-desc">配置系统参数和选项</div>
            </a>
            <a href="/admin/export" class="action-card">
                <span class="action-icon">📊</span>
                <div class="action-title">数据导出</div>
                <div class="action-desc">导出模板和签名数据</div>
            </a>
        </div>

        <!-- 最近签名 -->
        <div class="recent-section">
            <h2 class="section-title">最近签名记录</h2>
            <div id="recentSignatures">
                <div class="loading">
                    <div class="loading-spinner"></div>
                    <p>正在加载数据...</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 页面加载时获取统计数据
        document.addEventListener('DOMContentLoaded', function() {
            loadDashboardData();
            loadRecentSignatures();
        });

        // 加载仪表板数据
        async function loadDashboardData() {
            try {
                const response = await fetch('/api/admin/stats');
                if (response.ok) {
                    const data = await response.json();
                    updateStats(data);
                } else {
                    console.error('Failed to load dashboard data');
                    showError('加载统计数据失败');
                }
            } catch (error) {
                console.error('Error loading dashboard data:', error);
                showError('网络错误，请稍后重试');
            }
        }

        // 更新统计数据
        function updateStats(data) {
            document.getElementById('totalTemplates').textContent = data.totalTemplates || 0;
            document.getElementById('totalSignatures').textContent = data.totalSignatures || 0;
            document.getElementById('todaySignatures').textContent = data.todaySignatures || 0;
            document.getElementById('activeTemplates').textContent = data.activeTemplates || 0;
        }

        // 加载最近签名记录
        async function loadRecentSignatures() {
            try {
                const response = await fetch('/api/signatures?limit=10&sort=created_at&order=desc');
                if (response.ok) {
                    const data = await response.json();
                    renderRecentSignatures(data.signatures || []);
                } else {
                    console.error('Failed to load recent signatures');
                    showRecentSignaturesError();
                }
            } catch (error) {
                console.error('Error loading recent signatures:', error);
                showRecentSignaturesError();
            }
        }

        // 渲染最近签名记录
        function renderRecentSignatures(signatures) {
            const container = document.getElementById('recentSignatures');
            
            if (signatures.length === 0) {
                container.innerHTML = `
                    <div class="empty-state">
                        <div class="empty-icon">📝</div>
                        <p>暂无签名记录</p>
                    </div>
                `;
                return;
            }

            const tableHTML = `
                <table class="table">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>模板名称</th>
                            <th>签名人</th>
                            <th>签名时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${signatures.map(signature => `
                            <tr>
                                <td>#${signature.id}</td>
                                <td>${signature.template_name || '未知模板'}</td>
                                <td>${signature.user_name}</td>
                                <td>${formatDateTime(signature.signed_at)}</td>
                                <td>
                                    <a href="/admin/signatures/${signature.id}" class="btn btn-primary btn-sm">查看</a>
                                    <a href="/api/signatures/${signature.id}/preview" target="_blank" class="btn btn-secondary btn-sm">预览</a>
                                </td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            `;
            
            container.innerHTML = tableHTML;
        }

        // 显示最近签名记录错误
        function showRecentSignaturesError() {
            const container = document.getElementById('recentSignatures');
            container.innerHTML = `
                <div class="empty-state">
                    <div class="empty-icon">❌</div>
                    <p>加载签名记录失败</p>
                    <button class="btn btn-primary" onclick="loadRecentSignatures()">重试</button>
                </div>
            `;
        }

        // 显示错误消息
        function showError(message) {
            // 简单的错误提示，可以后续改为更好的UI组件
            alert(message);
        }

        // 格式化日期时间
        function formatDateTime(dateString) {
            if (!dateString) return '-';
            
            try {
                const date = new Date(dateString);
                return date.toLocaleString('zh-CN', {
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit',
                    hour: '2-digit',
                    minute: '2-digit'
                });
            } catch (error) {
                return dateString;
            }
        }

        // 自动刷新数据（每30秒）
        setInterval(() => {
            loadDashboardData();
        }, 30000);

        // 键盘快捷键
        document.addEventListener('keydown', function(e) {
            // Ctrl/Cmd + R 刷新数据
            if ((e.ctrlKey || e.metaKey) && e.key === 'r') {
                e.preventDefault();
                loadDashboardData();
                loadRecentSignatures();
            }
            
            // 数字键快捷导航
            if (e.altKey) {
                switch(e.key) {
                    case '1':
                        window.location.href = '/admin/templates';
                        break;
                    case '2':
                        window.location.href = '/admin/signatures';
                        break;
                    case '3':
                        window.location.href = '/admin/settings';
                        break;
                    case '4':
                        window.location.href = '/admin/export';
                        break;
                }
            }
        });
    </script>
</body>
</html>