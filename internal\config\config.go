package config

import (
	"os"
	"strconv"

	"gorm.io/gorm/logger"
)

// Config 应用配置结构
type Config struct {
	Port         string
	DatabasePath string
	StoragePath  string
	BaseURL      string
	Debug        bool
	LogLevel     logger.LogLevel
}

// Load 加载配置
func Load() *Config {
	debug := getEnvBool("DEBUG", false)
	logLevel := logger.Silent
	if debug {
		logLevel = logger.Info
	}

	return &Config{
		Port:         getEnv("PORT", "8080"),
		DatabasePath: getEnv("DATABASE_PATH", "./database/esign.db"),
		StoragePath:  getEnv("STORAGE_PATH", "./storage"),
		BaseURL:      getEnv("BASE_URL", "http://localhost:8080"),
		Debug:        debug,
		LogLevel:     logLevel,
	}
}

// getEnv 获取环境变量，如果不存在则返回默认值
func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

// getEnvBool 获取布尔类型环境变量
func getEnvBool(key string, defaultValue bool) bool {
	if value := os.Getenv(key); value != "" {
		if parsed, err := strconv.ParseBool(value); err == nil {
			return parsed
		}
	}
	return defaultValue
}
