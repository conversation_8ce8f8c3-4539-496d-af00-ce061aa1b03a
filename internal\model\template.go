package model

import (
	"time"
)

// Template PDF模板结构
type Template struct {
	ID              uint      `json:"id" gorm:"primaryKey"`
	Name            string    `json:"name" gorm:"not null"`
	FilePath        string    `json:"file_path" gorm:"not null"`
	SignatureX      float64   `json:"signature_x" gorm:"not null"`
	SignatureY      float64   `json:"signature_y" gorm:"not null"`
	SignatureWidth  float64   `json:"signature_width" gorm:"default:100"`
	SignatureHeight float64   `json:"signature_height" gorm:"default:50"`
	QRCodePath      string    `json:"qr_code_path"`
	CreatedAt       time.Time `json:"created_at"`
	UpdatedAt       time.Time `json:"updated_at"`
}

// CreateTemplateRequest 创建模板请求
type CreateTemplateRequest struct {
	Name            string  `json:"name" binding:"required"`
	SignatureX      float64 `json:"signature_x" binding:"required"`
	SignatureY      float64 `json:"signature_y" binding:"required"`
	SignatureWidth  float64 `json:"signature_width"`
	SignatureHeight float64 `json:"signature_height"`
}

// UpdateTemplateRequest 更新模板请求
type UpdateTemplateRequest struct {
	Name            string  `json:"name"`
	SignatureX      float64 `json:"signature_x"`
	SignatureY      float64 `json:"signature_y"`
	SignatureWidth  float64 `json:"signature_width"`
	SignatureHeight float64 `json:"signature_height"`
}
