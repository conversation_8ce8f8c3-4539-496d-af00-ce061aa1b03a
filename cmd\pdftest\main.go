package main

import (
	"encoding/json"
	"flag"
	"fmt"
	"log"
	"os"

	"esign/internal/service"
)

func main() {
	var (
		action       = flag.String("action", "", "Action to perform: validate, info, sign")
		pdfPath      = flag.String("pdf", "", "PDF file path")
		signaturePath = flag.String("signature", "", "Signature image path")
		outputPath   = flag.String("output", "", "Output PDF path")
		x            = flag.Float64("x", 100, "Signature X position")
		y            = flag.Float64("y", 100, "Signature Y position")
		width        = flag.Float64("width", 100, "Signature width")
		height       = flag.Float64("height", 50, "Signature height")
	)
	flag.Parse()

	if *action == "" || *pdfPath == "" {
		fmt.Println("Usage: pdftest -action <validate|info|sign> -pdf <path> [options]")
		fmt.Println("Options:")
		fmt.Println("  -signature <path>  Signature image path (for sign action)")
		fmt.Println("  -output <path>     Output PDF path (for sign action)")
		fmt.Println("  -x <float>         Signature X position (default: 100)")
		fmt.Println("  -y <float>         Signature Y position (default: 100)")
		fmt.Println("  -width <float>     Signature width (default: 100)")
		fmt.Println("  -height <float>    Signature height (default: 50)")
		os.Exit(1)
	}

	// 创建PDF服务
	pdfService := service.NewPDFService()

	switch *action {
	case "validate":
		validatePDF(pdfService, *pdfPath)
	case "info":
		showPDFInfo(pdfService, *pdfPath)
	case "sign":
		if *signaturePath == "" || *outputPath == "" {
			fmt.Println("Sign action requires -signature and -output parameters")
			os.Exit(1)
		}
		signPDF(pdfService, *pdfPath, *signaturePath, *outputPath, *x, *y, *width, *height)
	default:
		fmt.Printf("Unknown action: %s\n", *action)
		os.Exit(1)
	}
}

func validatePDF(pdfService service.PDFService, pdfPath string) {
	fmt.Printf("Validating PDF: %s\n", pdfPath)
	
	if err := pdfService.ValidatePDF(pdfPath); err != nil {
		fmt.Printf("❌ PDF validation failed: %v\n", err)
		os.Exit(1)
	}
	
	fmt.Println("✅ PDF is valid!")
}

func showPDFInfo(pdfService service.PDFService, pdfPath string) {
	fmt.Printf("Getting PDF info: %s\n", pdfPath)
	
	info, err := pdfService.GetPDFInfo(pdfPath)
	if err != nil {
		fmt.Printf("❌ Failed to get PDF info: %v\n", err)
		os.Exit(1)
	}
	
	// 格式化输出
	jsonData, err := json.MarshalIndent(info, "", "  ")
	if err != nil {
		log.Fatal("Failed to format PDF info:", err)
	}
	
	fmt.Println("PDF Information:")
	fmt.Println("================")
	fmt.Println(string(jsonData))
}

func signPDF(pdfService service.PDFService, pdfPath, signaturePath, outputPath string, x, y, width, height float64) {
	fmt.Printf("Signing PDF: %s\n", pdfPath)
	fmt.Printf("Signature: %s\n", signaturePath)
	fmt.Printf("Output: %s\n", outputPath)
	fmt.Printf("Position: (%.2f, %.2f), Size: (%.2f, %.2f)\n", x, y, width, height)
	
	config := &service.SignatureConfig{
		X:      x,
		Y:      y,
		Width:  width,
		Height: height,
		PageNo: 1,
	}
	
	if err := pdfService.CreateSignedPDF(pdfPath, signaturePath, outputPath, config); err != nil {
		fmt.Printf("❌ Failed to sign PDF: %v\n", err)
		os.Exit(1)
	}
	
	fmt.Println("✅ PDF signed successfully!")
}
