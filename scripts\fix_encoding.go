package main

import (
	"database/sql"
	"fmt"
	"log"
	"os"

	_ "modernc.org/sqlite"
)

func main() {
	if len(os.Args) < 2 {
		log.Fatal("Usage: go run fix_encoding.go <database_path>")
	}

	dbPath := os.Args[1]
	
	// 打开数据库连接
	dsn := dbPath + "?_pragma=foreign_keys(1)&_pragma=journal_mode(WAL)&_pragma=encoding(UTF-8)"
	db, err := sql.Open("sqlite", dsn)
	if err != nil {
		log.Fatalf("Failed to open database: %v", err)
	}
	defer db.Close()

	// 设置UTF-8编码
	if _, err := db.Exec("PRAGMA encoding = 'UTF-8'"); err != nil {
		log.Fatalf("Failed to set UTF-8 encoding: %v", err)
	}

	fmt.Println("Database encoding set to UTF-8")

	// 查询当前编码问题的记录
	rows, err := db.Query("SELECT id, signer_name FROM signatures WHERE signer_name LIKE '%♦%' OR signer_name LIKE '%�%'")
	if err != nil {
		log.Fatalf("Failed to query signatures: %v", err)
	}
	defer rows.Close()

	var problematicRecords []struct {
		ID         int
		SignerName string
	}

	for rows.Next() {
		var id int
		var signerName string
		if err := rows.Scan(&id, &signerName); err != nil {
			log.Printf("Error scanning row: %v", err)
			continue
		}
		problematicRecords = append(problematicRecords, struct {
			ID         int
			SignerName string
		}{ID: id, SignerName: signerName})
	}

	if len(problematicRecords) == 0 {
		fmt.Println("No encoding issues found in signatures table")
		return
	}

	fmt.Printf("Found %d records with encoding issues:\n", len(problematicRecords))
	for _, record := range problematicRecords {
		fmt.Printf("ID: %d, Current Name: %s\n", record.ID, record.SignerName)
	}

	// 提供修复建议
	fmt.Println("\nTo fix these records, you can:")
	fmt.Println("1. Update them manually using SQL:")
	fmt.Println("   UPDATE signatures SET signer_name = '正确的中文名' WHERE id = X;")
	fmt.Println("2. Or delete and recreate the signatures with proper encoding")
	
	// 创建备份
	fmt.Println("\nCreating backup...")
	backupPath := dbPath + ".backup"
	if err := createBackup(db, backupPath); err != nil {
		log.Printf("Failed to create backup: %v", err)
	} else {
		fmt.Printf("Backup created at: %s\n", backupPath)
	}
}

func createBackup(db *sql.DB, backupPath string) error {
	// 简单的备份方法：导出所有数据
	backupDB, err := sql.Open("sqlite", backupPath)
	if err != nil {
		return err
	}
	defer backupDB.Close()

	// 复制表结构和数据
	tables := []string{"templates", "signatures"}
	
	for _, table := range tables {
		// 获取表结构
		var createSQL string
		err := db.QueryRow(fmt.Sprintf("SELECT sql FROM sqlite_master WHERE type='table' AND name='%s'", table)).Scan(&createSQL)
		if err != nil {
			continue // 表可能不存在
		}

		// 在备份数据库中创建表
		if _, err := backupDB.Exec(createSQL); err != nil {
			return err
		}

		// 复制数据
		rows, err := db.Query(fmt.Sprintf("SELECT * FROM %s", table))
		if err != nil {
			continue
		}
		defer rows.Close()

		// 这里简化处理，实际项目中需要更复杂的数据复制逻辑
	}

	return nil
}
