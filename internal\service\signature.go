package service

import (
	"encoding/base64"
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"time"
	"unicode"

	"esign/internal/model"
	"esign/internal/repository"
)

// SignatureService 签署服务接口
type SignatureService interface {
	CreateSignature(templateID uint, req *model.CreateSignatureRequest, ipAddress, userAgent string) (*model.SignatureResponse, error)
	GetSignature(id uint) (*model.Signature, error)
	GetSignaturesByTemplate(templateID uint) ([]*model.Signature, error)
	GetAllSignatures() ([]*model.Signature, error)
	ProcessSignedDocument(signatureID uint) error
}

// signatureService 签署服务实现
type signatureService struct {
	signatureRepo repository.SignatureRepository
	templateRepo  repository.TemplateRepository
	pdfService    PDFService
}

// NewSignatureService 创建签署服务实例
func NewSignatureService(signatureRepo repository.SignatureRepository, templateRepo repository.TemplateRepository, pdfService PDFService) SignatureService {
	return &signatureService{
		signatureRepo: signatureRepo,
		templateRepo:  templateRepo,
		pdfService:    pdfService,
	}
}

// CreateSignature 创建签署记录
func (s *signatureService) CreateSignature(templateID uint, req *model.CreateSignatureRequest, ipAddress, userAgent string) (*model.SignatureResponse, error) {
	// 验证模板是否存在
	_, err := s.templateRepo.GetByID(templateID)
	if err != nil {
		return nil, fmt.Errorf("template not found: %w", err)
	}

	// 保存签名图片
	signaturePath, err := s.saveSignatureImage(req.SignatureData, templateID, req.SignerName)
	if err != nil {
		return nil, fmt.Errorf("failed to save signature image: %w", err)
	}

	// 生成签署后文档路径（这里先占位，实际PDF处理在PDF服务中完成）
	signedDocPath := s.generateSignedDocumentPath(templateID, req.SignerName)

	// 创建签署记录
	signature := &repository.Signature{
		TemplateID:         templateID,
		SignerName:         req.SignerName,
		SignaturePath:      signaturePath,
		SignedDocumentPath: signedDocPath,
		IPAddress:          ipAddress,
		UserAgent:          userAgent,
		SignedAt:           time.Now(),
	}

	err = s.signatureRepo.Create(signature)
	if err != nil {
		return nil, fmt.Errorf("failed to create signature record: %w", err)
	}

	return &model.SignatureResponse{
		ID:                 signature.ID,
		TemplateID:         signature.TemplateID,
		SignerName:         signature.SignerName,
		SignedDocumentPath: signature.SignedDocumentPath,
		SignedAt:           signature.SignedAt,
	}, nil
}

// GetSignature 获取签署记录
func (s *signatureService) GetSignature(id uint) (*model.Signature, error) {
	signature, err := s.signatureRepo.GetByID(id)
	if err != nil {
		return nil, err
	}
	return s.convertToModel(signature), nil
}

// GetSignaturesByTemplate 根据模板获取签署记录
func (s *signatureService) GetSignaturesByTemplate(templateID uint) ([]*model.Signature, error) {
	signatures, err := s.signatureRepo.GetByTemplateID(templateID)
	if err != nil {
		return nil, err
	}

	var result []*model.Signature
	for _, signature := range signatures {
		result = append(result, s.convertToModel(signature))
	}
	return result, nil
}

// GetAllSignatures 获取所有签署记录
func (s *signatureService) GetAllSignatures() ([]*model.Signature, error) {
	signatures, err := s.signatureRepo.GetAll()
	if err != nil {
		return nil, err
	}

	var result []*model.Signature
	for _, signature := range signatures {
		result = append(result, s.convertToModel(signature))
	}
	return result, nil
}

// saveSignatureImage 保存签名图片
func (s *signatureService) saveSignatureImage(signatureData string, templateID uint, signerName string) (string, error) {
	// 解码Base64数据
	data, err := base64.StdEncoding.DecodeString(signatureData)
	if err != nil {
		return "", err
	}

	// 生成文件名
	timestamp := time.Now().Format("20060102150405")
	safeName := s.sanitizeFileName(signerName)
	filename := fmt.Sprintf("signature_%d_%s_%s.png", templateID, safeName, timestamp)
	filePath := filepath.Join("storage", "signatures", filename)

	// 确保目录存在
	dir := filepath.Dir(filePath)
	if err := os.MkdirAll(dir, 0755); err != nil {
		return "", err
	}

	// 保存文件
	err = os.WriteFile(filePath, data, 0644)
	if err != nil {
		return "", err
	}

	return filePath, nil
}

// generateSignedDocumentPath 生成签署后文档路径
func (s *signatureService) generateSignedDocumentPath(templateID uint, signerName string) string {
	timestamp := time.Now().Format("20060102150405")
	safeName := s.sanitizeFileName(signerName)
	filename := fmt.Sprintf("signed_%d_%s_%s.pdf", templateID, safeName, timestamp)
	return filepath.Join("storage", "documents", filename)
}

// convertToModel 转换为模型
func (s *signatureService) convertToModel(signature *repository.Signature) *model.Signature {
	return &model.Signature{
		ID:                 signature.ID,
		TemplateID:         signature.TemplateID,
		SignerName:         signature.SignerName,
		SignaturePath:      signature.SignaturePath,
		SignedDocumentPath: signature.SignedDocumentPath,
		IPAddress:          signature.IPAddress,
		UserAgent:          signature.UserAgent,
		SignedAt:           signature.SignedAt,
		Template: model.Template{
			ID:              signature.Template.ID,
			Name:            signature.Template.Name,
			FilePath:        signature.Template.FilePath,
			SignatureX:      signature.Template.SignatureX,
			SignatureY:      signature.Template.SignatureY,
			SignatureWidth:  signature.Template.SignatureWidth,
			SignatureHeight: signature.Template.SignatureHeight,
			QRCodePath:      signature.Template.QRCodePath,
			CreatedAt:       signature.Template.CreatedAt,
			UpdatedAt:       signature.Template.UpdatedAt,
		},
	}
}

// ProcessSignedDocument 处理签署后的文档
func (s *signatureService) ProcessSignedDocument(signatureID uint) error {
	// 获取签署记录
	signature, err := s.signatureRepo.GetByID(signatureID)
	if err != nil {
		return fmt.Errorf("failed to get signature record: %w", err)
	}

	// 获取模板信息
	template, err := s.templateRepo.GetByID(signature.TemplateID)
	if err != nil {
		return fmt.Errorf("failed to get template: %w", err)
	}

	// 创建签名配置
	config := &SignatureConfig{
		X:      template.SignatureX,
		Y:      template.SignatureY,
		Width:  template.SignatureWidth,
		Height: template.SignatureHeight,
		PageNo: 1,
	}

	// 处理PDF签名
	err = s.pdfService.CreateSignedPDF(
		template.FilePath,
		signature.SignaturePath,
		signature.SignedDocumentPath,
		config,
	)
	if err != nil {
		return fmt.Errorf("failed to create signed PDF: %w", err)
	}

	return nil
}

// sanitizeFileName 清理文件名，确保文件系统兼容性
func (s *signatureService) sanitizeFileName(name string) string {
	if name == "" {
		return "unknown"
	}

	// 首先尝试提取ASCII字符
	var asciiPart strings.Builder
	var hasNonASCII bool

	for _, r := range name {
		if r <= 127 {
			if unicode.IsLetter(r) || unicode.IsDigit(r) {
				asciiPart.WriteRune(r)
			} else if r == ' ' || r == '-' || r == '_' {
				asciiPart.WriteString("_")
			}
		} else {
			hasNonASCII = true
		}
	}

	result := asciiPart.String()

	// 清理连续的下划线
	for strings.Contains(result, "__") {
		result = strings.ReplaceAll(result, "__", "_")
	}
	result = strings.Trim(result, "_")

	// 如果有非ASCII字符但ASCII部分太短，使用更友好的名称
	if hasNonASCII {
		if len(result) < 2 {
			// 根据原始名称长度生成友好的标识
			switch {
			case len(name) <= 2:
				result = "user_cn"
			case len(name) <= 4:
				result = "signer_cn"
			default:
				result = "chinese_user"
			}
		} else {
			// 保留ASCII部分并添加中文标识
			result = result + "_cn"
		}
	}

	// 如果结果仍然为空，使用默认名称
	if len(result) < 2 {
		result = "user"
	}

	// 限制长度
	if len(result) > 30 {
		result = result[:30]
	}

	return result
}
