<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简单签名测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            max-width: 600px;
            margin: 0 auto;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, button {
            width: 100%;
            padding: 12px;
            font-size: 16px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            cursor: pointer;
            margin-top: 10px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        #result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 4px;
            display: none;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <h1>简单签名测试</h1>
    
    <div class="form-group">
        <label for="signerName">签署人姓名:</label>
        <input type="text" id="signerName" value="简单测试用户" required>
    </div>
    
    <div class="form-group">
        <label for="templateId">模板ID:</label>
        <input type="number" id="templateId" value="4" required>
    </div>
    
    <button onclick="testSign()" id="signBtn">提交签名</button>
    <button onclick="testAPI()" id="apiBtn">测试API连接</button>
    
    <div id="result"></div>

    <script>
        // 显示结果
        function showResult(message, type = 'info') {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = message;
            resultDiv.className = type;
            resultDiv.style.display = 'block';
        }

        // 测试API连接
        async function testAPI() {
            const btn = document.getElementById('apiBtn');
            btn.disabled = true;
            btn.textContent = '测试中...';
            
            try {
                const response = await fetch('http://**********:8080/health');
                if (response.ok) {
                    const data = await response.json();
                    showResult(`✅ API连接正常<br>状态: ${data.status}<br>运行时间: ${data.uptime}`, 'success');
                } else {
                    showResult(`❌ API连接失败<br>状态码: ${response.status}`, 'error');
                }
            } catch (error) {
                showResult(`❌ 网络错误<br>错误: ${error.message}`, 'error');
            } finally {
                btn.disabled = false;
                btn.textContent = '测试API连接';
            }
        }

        // 测试签名提交
        async function testSign() {
            const btn = document.getElementById('signBtn');
            const signerName = document.getElementById('signerName').value;
            const templateId = document.getElementById('templateId').value;
            
            if (!signerName.trim()) {
                showResult('❌ 请输入签署人姓名', 'error');
                return;
            }
            
            btn.disabled = true;
            btn.textContent = '提交中...';
            
            const requestData = {
                signer_name: signerName,
                signature_data: "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg=="
            };
            
            try {
                console.log('发送请求到:', `http://**********:8080/api/sign/${templateId}`);
                console.log('请求数据:', requestData);
                
                const response = await fetch(`http://**********:8080/api/sign/${templateId}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify(requestData),
                    mode: 'cors'
                });
                
                console.log('响应状态:', response.status);
                console.log('响应头:', [...response.headers.entries()]);
                
                if (response.ok) {
                    const result = await response.json();
                    console.log('响应数据:', result);
                    showResult(`
                        ✅ 签名提交成功！<br>
                        签署ID: ${result.signature.id}<br>
                        签署人: ${result.signature.signer_name}<br>
                        签署时间: ${new Date(result.signature.signed_at).toLocaleString()}<br>
                        消息: ${result.message}
                    `, 'success');
                } else {
                    const errorText = await response.text();
                    console.error('请求失败:', errorText);
                    showResult(`❌ 签名提交失败<br>状态码: ${response.status}<br>错误: ${errorText}`, 'error');
                }
            } catch (error) {
                console.error('网络错误:', error);
                showResult(`❌ 网络错误<br>类型: ${error.name}<br>消息: ${error.message}`, 'error');
            } finally {
                btn.disabled = false;
                btn.textContent = '提交签名';
            }
        }

        // 页面加载时的初始化
        window.addEventListener('load', function() {
            console.log('页面加载完成');
            console.log('当前URL:', window.location.href);
            console.log('User Agent:', navigator.userAgent);
            console.log('支持fetch:', typeof fetch !== 'undefined');
            
            showResult('页面加载完成，可以开始测试', 'info');
        });
    </script>
</body>
</html>
