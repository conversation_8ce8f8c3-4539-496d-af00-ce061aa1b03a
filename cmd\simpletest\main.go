package main

import (
	"fmt"
	"image"
	"image/color"
	"image/draw"
	"image/png"
	"math"
	"os"
)

func main() {
	// 创建示例签名图片
	if err := createSampleSignature(); err != nil {
		fmt.Printf("Failed to create sample signature: %v\n", err)
		os.Exit(1)
	}

	fmt.Println("✅ Sample signature created successfully!")
	fmt.Println("  - storage/signatures/sample_signature.png")
}

func createSampleSignature() error {
	// 确保目录存在
	if err := os.MkdirAll("storage/signatures", 0755); err != nil {
		return err
	}

	// 创建签名图片 (120x60)
	width, height := 120, 60
	img := image.NewRGBA(image.Rect(0, 0, width, height))

	// 设置透明背景
	draw.Draw(img, img.Bounds(), &image.Uniform{color.RGBA{255, 255, 255, 0}}, image.Point{}, draw.Src)

	// 绘制简单的签名样式
	blue := color.RGBA{0, 0, 255, 255}
	
	// 绘制签名线条（模拟手写签名）
	for x := 10; x < width-10; x++ {
		y := height/2 + int(10*math.Sin(float64(x)/10))
		if y >= 0 && y < height {
			img.Set(x, y, blue)
			if y+1 < height {
				img.Set(x, y+1, blue)
			}
		}
	}

	// 添加签名文字
	drawText(img, "张三", 20, height-15, blue)

	// 保存PNG文件
	file, err := os.Create("storage/signatures/sample_signature.png")
	if err != nil {
		return err
	}
	defer file.Close()

	return png.Encode(file, img)
}

// 简单的文字绘制函数
func drawText(img *image.RGBA, text string, x, y int, c color.Color) {
	// 这里只是一个简单的实现，实际项目中应该使用字体库
	for i, char := range text {
		drawChar(img, char, x+i*8, y, c)
	}
}

func drawChar(img *image.RGBA, char rune, x, y int, c color.Color) {
	// 简单的字符绘制，只绘制几个像素点
	for dx := 0; dx < 6; dx++ {
		for dy := 0; dy < 8; dy++ {
			if (dx+dy)%2 == 0 {
				img.Set(x+dx, y+dy, c)
			}
		}
	}
}
