-- 修复数据库编码问题的SQL脚本

-- 设置数据库编码为UTF-8
PRAGMA encoding = 'UTF-8';

-- 查看当前有编码问题的记录
SELECT id, signer_name, template_id, signed_at 
FROM signatures 
WHERE signer_name LIKE '%♦%' OR signer_name LIKE '%�%' OR signer_name GLOB '*[^A-Za-z0-9 _-]*';

-- 手动修复已知的编码问题记录
-- 根据实际情况替换这些值

-- 示例修复（需要根据实际数据调整）
UPDATE signatures SET signer_name = '张三' WHERE id = 1 AND signer_name LIKE '%♦%';
UPDATE signatures SET signer_name = '李四' WHERE id = 2 AND signer_name LIKE '%♦%';
UPDATE signatures SET signer_name = '王五' WHERE id = 3 AND signer_name LIKE '%♦%';
UPDATE signatures SET signer_name = '签署人' WHERE id = 8 AND signer_name LIKE '%♦%';
UPDATE signatures SET signer_name = '李明' WHERE id = 9 AND signer_name LIKE '%♦%';
UPDATE signatures SET signer_name = '张三' WHERE id = 10 AND signer_name LIKE '%♦%';
UPDATE signatures SET signer_name = '李四' WHERE id = 11 AND signer_name LIKE '%♦%';
UPDATE signatures SET signer_name = '王五' WHERE id = 12 AND signer_name LIKE '%♦%';
UPDATE signatures SET signer_name = '赵六' WHERE id = 13 AND signer_name LIKE '%♦%';
UPDATE signatures SET signer_name = '孙七' WHERE id = 14 AND signer_name LIKE '%♦%';
UPDATE signatures SET signer_name = '周八' WHERE id = 15 AND signer_name LIKE '%♦%';
UPDATE signatures SET signer_name = '吴九' WHERE id = 16 AND signer_name LIKE '%♦%';
UPDATE signatures SET signer_name = '郑十' WHERE id = 17 AND signer_name LIKE '%♦%';
UPDATE signatures SET signer_name = '张三' WHERE id = 18 AND signer_name LIKE '%♦%';
UPDATE signatures SET signer_name = 'API测试用户' WHERE id = 19 AND signer_name LIKE '%♦%';
UPDATE signatures SET signer_name = 'CORS测试用户' WHERE id = 20 AND signer_name LIKE '%♦%';

-- 验证修复结果
SELECT id, signer_name, template_id, signed_at 
FROM signatures 
ORDER BY id;
