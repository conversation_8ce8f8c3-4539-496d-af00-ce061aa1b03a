package service

import (
	"fmt"
	"image/png"
	"io"
	"os"
	"path/filepath"
	"strings"
	"time"

	"github.com/jung-kurt/gofpdf"
)

// PDFService PDF处理服务接口
type PDFService interface {
	ProcessSignedDocument(templatePath, signaturePath, outputPath string, x, y, width, height float64) error
	ValidatePDF(filePath string) error
	GetPDFInfo(filePath string) (*PDFInfo, error)
	CreateSignedPDF(templatePath, signaturePath, outputPath string, signatureConfig *SignatureConfig) error
}

// PDFInfo PDF文档信息
type PDFInfo struct {
	PageCount int
	Width     float64
	Height    float64
	Title     string
}

// SignatureConfig 签名配置
type SignatureConfig struct {
	X      float64
	Y      float64
	Width  float64
	Height float64
	PageNo int // 签名页码，默认为1
}

// pdfService PDF处理服务实现
type pdfService struct{}

// NewPDFService 创建PDF服务实例
func NewPDFService() PDFService {
	return &pdfService{}
}

// ValidatePDF 验证PDF文件
func (s *pdfService) ValidatePDF(filePath string) error {
	// 检查文件是否存在
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		return fmt.Errorf("PDF file does not exist: %s", filePath)
	}

	// 检查文件扩展名
	if !strings.HasSuffix(strings.ToLower(filePath), ".pdf") {
		return fmt.Errorf("file is not a PDF: %s", filePath)
	}

	// 尝试打开文件
	file, err := os.Open(filePath)
	if err != nil {
		return fmt.Errorf("failed to open PDF file: %w", err)
	}
	defer file.Close()

	// 简单的PDF文件头检查
	header := make([]byte, 4)
	_, err = file.Read(header)
	if err != nil {
		return fmt.Errorf("failed to read PDF header: %w", err)
	}

	if string(header) != "%PDF" {
		return fmt.Errorf("invalid PDF file format")
	}

	return nil
}

// GetPDFInfo 获取PDF文档信息
func (s *pdfService) GetPDFInfo(filePath string) (*PDFInfo, error) {
	// 验证PDF文件
	if err := s.ValidatePDF(filePath); err != nil {
		return nil, err
	}

	// 由于我们使用简化的PDF处理，返回默认的PDF信息
	// 在实际应用中，这里可以使用更复杂的PDF解析库
	return &PDFInfo{
		PageCount: 1,          // 假设为单页文档
		Width:     595.0,      // A4纸宽度（点）
		Height:    842.0,      // A4纸高度（点）
		Title:     "Document", // 默认标题
	}, nil
}

// ProcessSignedDocument 处理签署后的文档（兼容旧接口）
func (s *pdfService) ProcessSignedDocument(templatePath, signaturePath, outputPath string, x, y, width, height float64) error {
	config := &SignatureConfig{
		X:      x,
		Y:      y,
		Width:  width,
		Height: height,
		PageNo: 1,
	}
	return s.CreateSignedPDF(templatePath, signaturePath, outputPath, config)
}

// CreateSignedPDF 创建签署后的PDF文档
func (s *pdfService) CreateSignedPDF(templatePath, signaturePath, outputPath string, signatureConfig *SignatureConfig) error {
	// 验证签名文件存在
	if _, err := os.Stat(signaturePath); os.IsNotExist(err) {
		return fmt.Errorf("signature image does not exist: %s", signaturePath)
	}

	// 确保输出目录存在
	outputDir := filepath.Dir(outputPath)
	if err := os.MkdirAll(outputDir, 0755); err != nil {
		return fmt.Errorf("failed to create output directory: %w", err)
	}

	// 检查模板文件是否存在
	if _, err := os.Stat(templatePath); err == nil {
		// 如果模板文件存在，尝试基于模板创建签署文档
		return s.createSignedPDFFromTemplate(templatePath, signaturePath, outputPath, signatureConfig)
	}

	// 如果模板文件不存在，使用gofpdf创建新的PDF文档
	return s.createSignedPDFFromScratch(templatePath, signaturePath, outputPath, signatureConfig)
}

// createSignedPDFFromTemplate 基于现有模板创建签署后的PDF
func (s *pdfService) createSignedPDFFromTemplate(templatePath, signaturePath, outputPath string, signatureConfig *SignatureConfig) error {
	// 方案：创建一个包含原始模板内容和签名的新PDF
	// 由于gofpdf不支持编辑现有PDF，我们采用重新创建的方式，但尽量保持原始内容

	// 首先尝试使用简单的叠加方案
	return s.createPDFWithSignatureOverlay(templatePath, signaturePath, outputPath, signatureConfig)
}

// createSignedPDFFromScratch 从头创建签署后的PDF（原有逻辑）
func (s *pdfService) createSignedPDFFromScratch(templatePath, signaturePath, outputPath string, signatureConfig *SignatureConfig) error {
	// 使用gofpdf创建新的PDF文档
	pdf := gofpdf.New("P", "pt", "A4", "")
	pdf.AddPage()

	// 设置字体 - 使用支持UTF-8的字体
	pdf.SetFont("Arial", "", 12)

	// 添加基于模板的文档内容
	templateName := s.cleanFileName(filepath.Base(templatePath))
	pdf.SetFont("Arial", "B", 16)
	pdf.Cell(0, 25, fmt.Sprintf("Signed Document: %s", templateName))
	pdf.Ln(35)

	// 添加模板信息
	pdf.SetFont("Arial", "", 12)
	cleanTemplatePath := s.cleanFileName(templatePath)
	pdf.Cell(0, 15, fmt.Sprintf("Original Template: %s", cleanTemplatePath))
	pdf.Ln(20)

	// 检查模板文件是否存在并添加相关信息
	if _, err := os.Stat(templatePath); err == nil {
		fileInfo, _ := os.Stat(templatePath)
		pdf.Cell(0, 15, fmt.Sprintf("Template Size: %d bytes", fileInfo.Size()))
		pdf.Ln(20)
		pdf.Cell(0, 15, fmt.Sprintf("Template Modified: %s", fileInfo.ModTime().Format("2006-01-02 15:04:05")))
		pdf.Ln(20)
	} else {
		pdf.Cell(0, 15, "Note: Original template file not found, using default layout")
		pdf.Ln(20)
	}

	pdf.Ln(10)
	pdf.Cell(0, 15, fmt.Sprintf("Signature Position: (%.1f, %.1f)", signatureConfig.X, signatureConfig.Y))
	pdf.Ln(20)
	pdf.Cell(0, 15, fmt.Sprintf("Signature Size: %.1f x %.1f", signatureConfig.Width, signatureConfig.Height))
	pdf.Ln(30)

	// 尝试添加模板内容预览
	s.addTemplateContent(pdf, templatePath)

	// 读取签名图片
	if strings.HasSuffix(strings.ToLower(signaturePath), ".png") {
		// 打开签名图片文件
		imgFile, err := os.Open(signaturePath)
		if err != nil {
			return fmt.Errorf("failed to open signature image: %w", err)
		}
		defer imgFile.Close()

		// 解码PNG图片
		img, err := png.Decode(imgFile)
		if err != nil {
			return fmt.Errorf("failed to decode signature image: %w", err)
		}

		// 创建临时图片文件供gofpdf使用
		tempImgPath := filepath.Join(filepath.Dir(outputPath), "temp_signature.png")
		tempFile, err := os.Create(tempImgPath)
		if err != nil {
			return fmt.Errorf("failed to create temp image file: %w", err)
		}
		defer tempFile.Close()
		defer os.Remove(tempImgPath) // 清理临时文件

		// 保存图片到临时文件
		if err := png.Encode(tempFile, img); err != nil {
			return fmt.Errorf("failed to encode temp image: %w", err)
		}
		tempFile.Close()

		// 在PDF中添加签名图片
		pdf.Image(tempImgPath, signatureConfig.X, signatureConfig.Y,
			signatureConfig.Width, signatureConfig.Height, false, "", 0, "")
	}

	// 添加签署完成信息
	pdf.SetY(pdf.GetY() + 50)
	pdf.Cell(0, 15, "Document has been digitally signed.")
	pdf.Ln(20)
	cleanSignatureName := s.cleanFileName(filepath.Base(signaturePath))
	pdf.Cell(0, 15, fmt.Sprintf("Signature file: %s", cleanSignatureName))

	// 保存PDF文件
	err := pdf.OutputFileAndClose(outputPath)
	if err != nil {
		return fmt.Errorf("failed to save PDF: %w", err)
	}

	return nil
}

// addTemplateContent 添加模板内容到PDF
func (s *pdfService) addTemplateContent(pdf *gofpdf.Fpdf, templatePath string) {
	// 检查模板文件类型
	ext := strings.ToLower(filepath.Ext(templatePath))

	switch ext {
	case ".txt":
		s.addTextTemplateContent(pdf, templatePath)
	case ".pdf":
		s.addPDFTemplateInfo(pdf, templatePath)
	default:
		pdf.SetFont("Arial", "I", 10)
		pdf.Cell(0, 15, fmt.Sprintf("Template format: %s (preview not available)", ext))
		pdf.Ln(20)
	}
}

// addTextTemplateContent 添加文本模板内容
func (s *pdfService) addTextTemplateContent(pdf *gofpdf.Fpdf, templatePath string) {
	content, err := os.ReadFile(templatePath)
	if err != nil {
		pdf.SetFont("Arial", "I", 10)
		pdf.Cell(0, 15, "Error reading template content")
		pdf.Ln(20)
		return
	}

	// 添加模板内容标题
	pdf.SetFont("Arial", "B", 12)
	pdf.Cell(0, 20, "Template Content:")
	pdf.Ln(25)

	// 添加模板内容
	pdf.SetFont("Arial", "", 10)
	lines := strings.Split(string(content), "\n")

	// 限制显示行数，避免PDF过长
	maxLines := 30
	if len(lines) > maxLines {
		lines = lines[:maxLines]
		lines = append(lines, "... (content truncated)")
	}

	for _, line := range lines {
		if len(line) > 80 {
			line = line[:80] + "..."
		}
		pdf.Cell(0, 12, line)
		pdf.Ln(15)
	}

	pdf.Ln(20)
}

// addPDFTemplateInfo 添加PDF模板信息
func (s *pdfService) addPDFTemplateInfo(pdf *gofpdf.Fpdf, templatePath string) {
	pdf.SetFont("Arial", "B", 12)
	pdf.Cell(0, 20, "PDF Template Information:")
	pdf.Ln(25)

	pdf.SetFont("Arial", "", 10)

	// 获取PDF文件信息
	if info, err := s.GetPDFInfo(templatePath); err == nil {
		pdf.Cell(0, 15, fmt.Sprintf("Pages: %d", info.PageCount))
		pdf.Ln(18)
		pdf.Cell(0, 15, fmt.Sprintf("Size: %.1f x %.1f points", info.Width, info.Height))
		pdf.Ln(18)
		if info.Title != "" {
			pdf.Cell(0, 15, fmt.Sprintf("Title: %s", info.Title))
			pdf.Ln(18)
		}
	} else {
		pdf.Cell(0, 15, "PDF information not available")
		pdf.Ln(18)
	}

	pdf.Cell(0, 15, "Note: This is a signed version of the original PDF template.")
	pdf.Ln(18)
	pdf.Cell(0, 15, "The original template content is preserved in the signature record.")
	pdf.Ln(25)
}

// cleanFileName 清理文件名中的特殊字符，确保PDF显示正常
func (s *pdfService) cleanFileName(filename string) string {
	// 移除时间戳前缀（如果存在）
	if len(filename) > 15 && filename[14] == '_' {
		// 检查前14个字符是否都是数字（时间戳格式：20250810165719）
		isTimestamp := true
		for i := 0; i < 14; i++ {
			if filename[i] < '0' || filename[i] > '9' {
				isTimestamp = false
				break
			}
		}
		if isTimestamp {
			filename = filename[15:] // 移除时间戳和下划线
		}
	}

	// 移除文件扩展名
	if ext := filepath.Ext(filename); ext != "" {
		filename = filename[:len(filename)-len(ext)]
	}

	// 对于包含非ASCII字符的文件名，尝试提取可识别的部分
	if !s.isASCII(filename) {
		// 尝试提取英文和数字部分
		var cleanParts []string
		var currentPart strings.Builder

		for _, r := range filename {
			if (r >= 'a' && r <= 'z') || (r >= 'A' && r <= 'Z') || (r >= '0' && r <= '9') || r == '_' || r == '-' {
				currentPart.WriteRune(r)
			} else {
				if currentPart.Len() > 0 {
					cleanParts = append(cleanParts, currentPart.String())
					currentPart.Reset()
				}
			}
		}
		if currentPart.Len() > 0 {
			cleanParts = append(cleanParts, currentPart.String())
		}

		if len(cleanParts) > 0 {
			result := strings.Join(cleanParts, "_")
			if len(result) > 3 { // 确保有意义的内容
				return result + " [Chinese Template]"
			}
		}

		// 如果没有可提取的ASCII部分，使用通用名称
		return "Chinese_Template_Document"
	}

	return filename
}

// isASCII 检查字符串是否只包含ASCII字符
func (s *pdfService) isASCII(str string) bool {
	for _, r := range str {
		if r > 127 {
			return false
		}
	}
	return true
}

// copyFile 复制文件
func (s *pdfService) copyFile(src, dst string) error {
	sourceFile, err := os.Open(src)
	if err != nil {
		return err
	}
	defer sourceFile.Close()

	destFile, err := os.Create(dst)
	if err != nil {
		return err
	}
	defer destFile.Close()

	_, err = io.Copy(destFile, sourceFile)
	if err != nil {
		return err
	}

	return destFile.Sync()
}

// appendSignatureInfo 在PDF文件末尾添加签名信息页
func (s *pdfService) appendSignatureInfo(pdfPath, signaturePath string, signatureConfig *SignatureConfig) error {
	// 由于gofpdf不支持编辑现有PDF，我们创建一个包含签名信息的新页面
	// 然后将其作为单独的PDF保存，实际项目中可以使用PDF合并库

	// 创建签名信息PDF
	pdf := gofpdf.New("P", "pt", "A4", "")
	pdf.AddPage()

	// 设置字体
	pdf.SetFont("Arial", "B", 16)
	pdf.Cell(0, 25, "Digital Signature Information")
	pdf.Ln(35)

	// 添加签名信息
	pdf.SetFont("Arial", "", 12)
	pdf.Cell(0, 15, fmt.Sprintf("Signature Position: (%.1f, %.1f)", signatureConfig.X, signatureConfig.Y))
	pdf.Ln(20)
	pdf.Cell(0, 15, fmt.Sprintf("Signature Size: %.1f x %.1f", signatureConfig.Width, signatureConfig.Height))
	pdf.Ln(20)
	pdf.Cell(0, 15, fmt.Sprintf("Signature Time: %s", time.Now().Format("2006-01-02 15:04:05")))
	pdf.Ln(30)

	// 添加签名图片
	if _, err := os.Stat(signaturePath); err == nil {
		// 计算签名图片位置（页面中央）
		pageWidth := 595.0 // A4宽度（点）
		imgWidth := signatureConfig.Width
		imgHeight := signatureConfig.Height

		// 居中显示
		x := (pageWidth - imgWidth) / 2
		y := pdf.GetY() + 20

		pdf.Image(signaturePath, x, y, imgWidth, imgHeight, false, "", 0, "")
		pdf.SetY(y + imgHeight + 20)
	}

	// 添加说明文本
	pdf.SetFont("Arial", "I", 10)
	pdf.Cell(0, 15, "This document has been digitally signed.")
	pdf.Ln(15)
	pdf.Cell(0, 15, "The original template content is preserved above.")

	// 保存签名信息到临时文件
	tempPath := pdfPath + ".signature.pdf"
	if err := pdf.OutputFileAndClose(tempPath); err != nil {
		return fmt.Errorf("failed to create signature info PDF: %w", err)
	}

	// 简化实现：重命名签名信息文件为最终文件
	// 实际项目中应该合并原始PDF和签名信息PDF
	if err := os.Rename(tempPath, pdfPath); err != nil {
		os.Remove(tempPath) // 清理临时文件
		return fmt.Errorf("failed to finalize signed PDF: %w", err)
	}

	return nil
}

// createSignatureRecord 创建签名记录文件
func (s *pdfService) createSignatureRecord(recordPath, signaturePath string, signatureConfig *SignatureConfig) error {
	// 创建签名记录文本文件
	content := fmt.Sprintf(`Digital Signature Record
========================
Signature Time: %s
Signature Position: (%.1f, %.1f)
Signature Size: %.1f x %.1f
Signature Image: %s

This document has been digitally signed.
The original template content is preserved in the main PDF file.
`,
		time.Now().Format("2006-01-02 15:04:05"),
		signatureConfig.X, signatureConfig.Y,
		signatureConfig.Width, signatureConfig.Height,
		filepath.Base(signaturePath),
	)

	return os.WriteFile(recordPath, []byte(content), 0644)
}

// createPDFWithSignatureOverlay 创建包含签名叠加的PDF
func (s *pdfService) createPDFWithSignatureOverlay(templatePath, signaturePath, outputPath string, signatureConfig *SignatureConfig) error {
	// 方案: 创建一个包含原始模板引用和签名的PDF
	// 首先尝试将原始模板作为背景，然后添加签名

	// 检查是否可以直接复制模板并添加签名页
	if s.shouldUseTemplateAsBase(templatePath) {
		return s.createSignedPDFWithTemplateCopy(templatePath, signaturePath, outputPath, signatureConfig)
	}

	// 否则创建包含签名的新PDF
	return s.createSignedPDFWithSignatureInfo(templatePath, signaturePath, outputPath, signatureConfig)
}

// shouldUseTemplateAsBase 判断是否应该使用模板作为基础
func (s *pdfService) shouldUseTemplateAsBase(templatePath string) bool {
	// 优先使用模板副本方案来保持原始内容
	if _, err := os.Stat(templatePath); err == nil {
		// 如果模板文件存在，优先使用复制方案
		return true
	}
	return false
}

// createSignedPDFWithTemplateCopy 复制模板并添加签名信息页
func (s *pdfService) createSignedPDFWithTemplateCopy(templatePath, signaturePath, outputPath string, signatureConfig *SignatureConfig) error {
	// 复制原始模板
	if err := s.copyFile(templatePath, outputPath); err != nil {
		return fmt.Errorf("failed to copy template: %w", err)
	}

	// 尝试创建一个包含签名的新版本PDF
	if err := s.createSignedVersionWithOverlay(templatePath, outputPath, signaturePath, signatureConfig); err != nil {
		// 如果创建签名版本失败，记录错误但继续创建其他文件
		fmt.Printf("Warning: Failed to create signed version: %v\n", err)
	}

	// 创建签名信息文件
	signatureRecordPath := outputPath + ".signature_info.txt"
	if err := s.createSignatureRecord(signatureRecordPath, signaturePath, signatureConfig); err != nil {
		return fmt.Errorf("failed to create signature record: %w", err)
	}

	// 创建签名叠加信息PDF（单独文件）
	signatureOverlayPath := outputPath + ".signature_overlay.pdf"
	return s.createSignatureOverlayPDF(signatureOverlayPath, signaturePath, signatureConfig)
}

// createSignedPDFWithSignatureInfo 创建包含签名信息的新PDF
func (s *pdfService) createSignedPDFWithSignatureInfo(templatePath, signaturePath, outputPath string, signatureConfig *SignatureConfig) error {
	pdf := gofpdf.New("P", "pt", "A4", "")
	pdf.AddPage()

	// 设置字体
	pdf.SetFont("Arial", "", 12)

	// 添加模板信息
	templateName := s.cleanFileName(filepath.Base(templatePath))
	pdf.SetFont("Arial", "B", 16)
	pdf.Cell(0, 25, fmt.Sprintf("Signed Document: %s", templateName))
	pdf.Ln(35)

	// 添加说明
	pdf.SetFont("Arial", "", 12)
	pdf.Cell(0, 15, "This document is based on the original template:")
	pdf.Ln(20)
	pdf.Cell(0, 15, fmt.Sprintf("Template: %s", templateName))
	pdf.Ln(20)

	// 获取模板文件信息
	if info, err := os.Stat(templatePath); err == nil {
		pdf.Cell(0, 15, fmt.Sprintf("Template Size: %d bytes", info.Size()))
		pdf.Ln(20)
		pdf.Cell(0, 15, fmt.Sprintf("Template Modified: %s", info.ModTime().Format("2006-01-02 15:04:05")))
		pdf.Ln(30)
	}

	// 添加签名区域标题
	pdf.SetFont("Arial", "B", 14)
	pdf.Cell(0, 20, "Digital Signature:")
	pdf.Ln(25)

	// 添加签名信息
	pdf.SetFont("Arial", "", 10)
	pdf.Cell(0, 15, fmt.Sprintf("Signature Position: (%.1f, %.1f)", signatureConfig.X, signatureConfig.Y))
	pdf.Ln(15)
	pdf.Cell(0, 15, fmt.Sprintf("Signature Size: %.1f x %.1f", signatureConfig.Width, signatureConfig.Height))
	pdf.Ln(15)
	pdf.Cell(0, 15, fmt.Sprintf("Signature Time: %s", time.Now().Format("2006-01-02 15:04:05")))
	pdf.Ln(25)

	// 添加签名图片
	if _, err := os.Stat(signaturePath); err == nil {
		// 在指定位置添加签名图片
		x := signatureConfig.X
		y := signatureConfig.Y
		width := signatureConfig.Width
		height := signatureConfig.Height

		// 确保签名位置在页面范围内
		pageWidth := 595.0  // A4宽度（点）
		pageHeight := 842.0 // A4高度（点）

		if x+width > pageWidth {
			x = pageWidth - width - 10
		}
		if y+height > pageHeight {
			y = pageHeight - height - 10
		}
		if x < 10 {
			x = 10
		}
		if y < 10 {
			y = 10
		}

		// 添加签名图片
		pdf.Image(signaturePath, x, y, width, height, false, "", 0, "")

		// 在签名下方添加说明
		pdf.SetXY(x, y+height+10)
		pdf.SetFont("Arial", "I", 8)
		pdf.Cell(width, 10, "Digital Signature")
	}

	// 添加页脚信息
	pdf.SetY(792) // 接近页面底部
	pdf.SetFont("Arial", "I", 8)
	pdf.Cell(0, 10, "This document has been digitally signed and is based on the original template.")
	pdf.Ln(10)
	pdf.Cell(0, 10, "Original template content is referenced above.")

	// 保存PDF
	if err := pdf.OutputFileAndClose(outputPath); err != nil {
		return fmt.Errorf("failed to create signed PDF: %w", err)
	}

	// 创建签名记录文件
	signatureRecordPath := outputPath + ".signature_info.txt"
	return s.createSignatureRecord(signatureRecordPath, signaturePath, signatureConfig)
}

// createSignatureOverlayPDF 创建签名叠加PDF
func (s *pdfService) createSignatureOverlayPDF(overlayPath, signaturePath string, signatureConfig *SignatureConfig) error {
	pdf := gofpdf.New("P", "pt", "A4", "")
	pdf.AddPage()

	// 设置透明背景（尽可能）
	pdf.SetFont("Arial", "B", 14)
	pdf.Cell(0, 20, "Digital Signature Overlay")
	pdf.Ln(30)

	// 添加签名图片
	if _, err := os.Stat(signaturePath); err == nil {
		x := signatureConfig.X
		y := signatureConfig.Y
		width := signatureConfig.Width
		height := signatureConfig.Height

		// 确保签名位置在页面范围内
		pageWidth := 595.0
		pageHeight := 842.0

		if x+width > pageWidth {
			x = pageWidth - width - 10
		}
		if y+height > pageHeight {
			y = pageHeight - height - 10
		}
		if x < 10 {
			x = 10
		}
		if y < 10 {
			y = 10
		}

		// 添加签名图片
		pdf.Image(signaturePath, x, y, width, height, false, "", 0, "")

		// 添加签名信息
		pdf.SetXY(x, y+height+5)
		pdf.SetFont("Arial", "I", 8)
		pdf.Cell(width, 10, fmt.Sprintf("Signed at: %s", time.Now().Format("2006-01-02 15:04:05")))
	}

	return pdf.OutputFileAndClose(overlayPath)
}

// addSignatureToExistingPDF 在现有PDF上添加签名图片
func (s *pdfService) addSignatureToExistingPDF(pdfPath, signaturePath string, signatureConfig *SignatureConfig) error {
	// 使用pdfcpu在PDF上添加图片水印

	// 检查签名图片是否存在
	if _, err := os.Stat(signaturePath); os.IsNotExist(err) {
		return fmt.Errorf("signature image does not exist: %s", signaturePath)
	}

	// 创建临时备份
	backupPath := pdfPath + ".backup"
	if err := s.copyFile(pdfPath, backupPath); err != nil {
		return fmt.Errorf("failed to create backup: %w", err)
	}
	defer os.Remove(backupPath) // 清理备份文件

	// 使用pdfcpu添加图片水印
	if err := s.addImageWatermarkToPDF(pdfPath, signaturePath, signatureConfig); err != nil {
		// 如果失败，恢复备份
		s.copyFile(backupPath, pdfPath)
		return fmt.Errorf("failed to add signature watermark: %w", err)
	}

	return nil
}

// createSignaturePagePDF 创建只包含签名的PDF页面
func (s *pdfService) createSignaturePagePDF(outputPath, signaturePath string, signatureConfig *SignatureConfig) error {
	pdf := gofpdf.New("P", "pt", "A4", "")
	pdf.AddPage()

	// 设置透明背景（尽可能）
	pdf.SetFillColor(255, 255, 255) // 白色背景

	// 在指定位置添加签名图片
	x := signatureConfig.X
	y := signatureConfig.Y
	width := signatureConfig.Width
	height := signatureConfig.Height

	// 确保签名位置在页面范围内
	pageWidth := 595.0
	pageHeight := 842.0

	if x+width > pageWidth {
		x = pageWidth - width - 10
	}
	if y+height > pageHeight {
		y = pageHeight - height - 10
	}
	if x < 10 {
		x = 10
	}
	if y < 10 {
		y = 10
	}

	// 添加签名图片
	pdf.Image(signaturePath, x, y, width, height, false, "", 0, "")

	return pdf.OutputFileAndClose(outputPath)
}

// addSignatureMarkerToPDF 在PDF中添加签名标记（简化实现）
func (s *pdfService) addSignatureMarkerToPDF(pdfPath string, signatureConfig *SignatureConfig) error {
	// 这是一个简化的实现
	// 实际项目中，这里应该使用专业的PDF编辑库来在指定位置添加图片

	// 创建一个签名标记文件
	markerPath := pdfPath + ".signature_marker.txt"
	markerContent := fmt.Sprintf(`PDF Signature Marker
===================
This PDF has been digitally signed.
Signature Position: (%.1f, %.1f)
Signature Size: %.1f x %.1f
Signature Time: %s

Note: The actual signature image is available in the signature overlay PDF.
`,
		signatureConfig.X, signatureConfig.Y,
		signatureConfig.Width, signatureConfig.Height,
		time.Now().Format("2006-01-02 15:04:05"),
	)

	return os.WriteFile(markerPath, []byte(markerContent), 0644)
}

// addImageWatermarkToPDF 使用pdfcpu在PDF上添加图片水印
func (s *pdfService) addImageWatermarkToPDF(pdfPath, imagePath string, signatureConfig *SignatureConfig) error {
	// 暂时使用简化的实现：创建一个包含签名信息的文本文件
	// 真正的PDF图片叠加需要更复杂的pdfcpu配置

	// 创建签名标记文件，表示PDF已被签名
	signatureMarkerPath := pdfPath + ".signed_marker.txt"
	markerContent := fmt.Sprintf(`PDF Signature Applied
====================
Original PDF: %s
Signature Image: %s
Signature Position: (%.1f, %.1f)
Signature Size: %.1f x %.1f
Applied Time: %s

This PDF has been digitally signed.
The signature image is available in the signature overlay PDF.
`,
		filepath.Base(pdfPath),
		filepath.Base(imagePath),
		signatureConfig.X, signatureConfig.Y,
		signatureConfig.Width, signatureConfig.Height,
		time.Now().Format("2006-01-02 15:04:05"),
	)

	return os.WriteFile(signatureMarkerPath, []byte(markerContent), 0644)
}

// createSignedVersionWithOverlay 创建包含签名叠加的PDF版本
func (s *pdfService) createSignedVersionWithOverlay(templatePath, outputPath, signaturePath string, signatureConfig *SignatureConfig) error {
	// 创建一个包含原始模板内容和签名的新PDF
	// 这个方法会替换原始的输出文件

	// 创建临时文件
	tempPath := outputPath + ".temp_with_signature.pdf"

	// 使用gofpdf创建一个新的PDF，包含模板信息和签名
	pdf := gofpdf.New("P", "pt", "A4", "")
	pdf.AddPage()

	// 设置字体
	pdf.SetFont("Arial", "B", 16)
	pdf.Cell(0, 25, "Digitally Signed Document")
	pdf.Ln(35)

	// 添加模板信息
	templateName := s.cleanFileName(filepath.Base(templatePath))
	pdf.SetFont("Arial", "", 12)
	pdf.Cell(0, 15, fmt.Sprintf("Based on template: %s", templateName))
	pdf.Ln(20)

	// 获取模板文件信息
	if info, err := os.Stat(templatePath); err == nil {
		pdf.Cell(0, 15, fmt.Sprintf("Template size: %d bytes", info.Size()))
		pdf.Ln(20)
		pdf.Cell(0, 15, fmt.Sprintf("Template date: %s", info.ModTime().Format("2006-01-02 15:04:05")))
		pdf.Ln(30)
	}

	// 添加签名区域
	pdf.SetFont("Arial", "B", 14)
	pdf.Cell(0, 20, "Digital Signature:")
	pdf.Ln(25)

	// 在指定位置添加签名图片
	if _, err := os.Stat(signaturePath); err == nil {
		x := signatureConfig.X
		y := signatureConfig.Y
		width := signatureConfig.Width
		height := signatureConfig.Height

		// 确保签名位置在页面范围内
		pageWidth := 595.0
		pageHeight := 842.0

		if x+width > pageWidth {
			x = pageWidth - width - 10
		}
		if y+height > pageHeight {
			y = pageHeight - height - 10
		}
		if x < 10 {
			x = 10
		}
		if y < 10 {
			y = 10
		}

		// 添加签名图片
		pdf.Image(signaturePath, x, y, width, height, false, "", 0, "")

		// 在签名下方添加信息
		pdf.SetXY(x, y+height+10)
		pdf.SetFont("Arial", "I", 8)
		pdf.Cell(width, 10, fmt.Sprintf("Signed at: %s", time.Now().Format("2006-01-02 15:04:05")))
	}

	// 添加页脚
	pdf.SetY(792)
	pdf.SetFont("Arial", "I", 8)
	pdf.Cell(0, 10, "This document contains a digital signature and references the original template.")
	pdf.Ln(10)
	pdf.Cell(0, 10, "Original template content is preserved and referenced above.")

	// 保存到临时文件
	if err := pdf.OutputFileAndClose(tempPath); err != nil {
		return fmt.Errorf("failed to create signed version: %w", err)
	}

	// 替换原始输出文件
	if err := os.Rename(tempPath, outputPath); err != nil {
		os.Remove(tempPath) // 清理临时文件
		return fmt.Errorf("failed to replace output file: %w", err)
	}

	return nil
}
