-- 精确修复特定记录的编码问题

-- 设置数据库编码
PRAGMA encoding = 'UTF-8';

-- 修复具体的编码问题记录
UPDATE signatures SET signer_name = '测试用户' WHERE id = 4;
UPDATE signatures SET signer_name = '功能测试用户' WHERE id = 5;
UPDATE signatures SET signer_name = '修改测试用户' WHERE id = 6;
UPDATE signatures SET signer_name = '资源测试用户' WHERE id = 7;
UPDATE signatures SET signer_name = '小明' WHERE id = 9;
UPDATE signatures SET signer_name = 'API测试用户' WHERE id = 13;
UPDATE signatures SET signer_name = 'CORS测试用户' WHERE id = 15;
UPDATE signatures SET signer_name = '模板测试用户' WHERE id = 21;
UPDATE signatures SET signer_name = '中文编码测试' WHERE id = 24;
UPDATE signatures SET signer_name = '张三测试' WHERE id = 25;
UPDATE signatures SET signer_name = '李明' WHERE id = 26;
UPDATE signatures SET signer_name = '<PERSON>张三' WHERE id = 27;

-- 验证修复结果
SELECT 'After fix:' as status;
SELECT id, signer_name, template_id 
FROM signatures 
WHERE id IN (4,5,6,7,9,13,15,21,24,25,26,27)
ORDER BY id;
