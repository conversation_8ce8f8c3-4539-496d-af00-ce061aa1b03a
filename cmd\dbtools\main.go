package main

import (
	"encoding/json"
	"flag"
	"fmt"
	"log"
	"os"
	"time"

	"esign/internal/config"
	"esign/internal/database"
)

func main() {
	var (
		action     = flag.String("action", "", "Action to perform: init, stats, backup, health, seed")
		backupPath = flag.String("backup", "", "Backup file path (for backup action)")
	)
	flag.Parse()

	if *action == "" {
		fmt.Println("Usage: dbtools -action <init|stats|backup|health|seed> [-backup <path>]")
		os.Exit(1)
	}

	// 加载配置
	cfg := config.Load()

	// 初始化数据库
	dbConfig := &database.Config{
		DatabasePath: cfg.DatabasePath,
		LogLevel:     cfg.LogLevel,
	}

	db, err := database.NewDatabase(dbConfig)
	if err != nil {
		log.Fatal("Failed to initialize database:", err)
	}
	defer db.Close()

	switch *action {
	case "init":
		initDatabase(db)
	case "stats":
		showStats(db)
	case "backup":
		if *backupPath == "" {
			*backupPath = fmt.Sprintf("backup_%s.db", time.Now().Format("20060102_150405"))
		}
		backupDatabase(db, *backupPath)
	case "health":
		healthCheck(db)
	case "seed":
		seedDatabase(db)
	default:
		fmt.Printf("Unknown action: %s\n", *action)
		os.Exit(1)
	}
}

func initDatabase(db *database.Database) {
	fmt.Println("Initializing database...")

	// 自动迁移
	if err := db.AutoMigrate(); err != nil {
		log.Fatal("Failed to migrate database:", err)
	}

	fmt.Println("Database initialized successfully!")
}

func showStats(db *database.Database) {
	fmt.Println("Database Statistics:")
	fmt.Println("===================")

	stats, err := db.GetStats()
	if err != nil {
		log.Fatal("Failed to get database stats:", err)
	}

	// 格式化输出
	jsonData, err := json.MarshalIndent(stats, "", "  ")
	if err != nil {
		log.Fatal("Failed to format stats:", err)
	}

	fmt.Println(string(jsonData))
}

func backupDatabase(db *database.Database, backupPath string) {
	fmt.Printf("Backing up database to: %s\n", backupPath)

	if err := db.Backup(backupPath); err != nil {
		log.Fatal("Failed to backup database:", err)
	}

	fmt.Println("Database backup completed successfully!")
}

func healthCheck(db *database.Database) {
	fmt.Println("Performing database health check...")

	if err := db.HealthCheck(); err != nil {
		fmt.Printf("❌ Database health check failed: %v\n", err)
		os.Exit(1)
	}

	fmt.Println("✅ Database is healthy!")
}

func seedDatabase(db *database.Database) {
	fmt.Println("Seeding database with sample data...")

	// 读取种子数据文件
	seedData, err := os.ReadFile("database/seeds.sql")
	if err != nil {
		log.Fatal("Failed to read seeds.sql:", err)
	}

	// 执行种子数据SQL
	if err := db.GetDB().Exec(string(seedData)).Error; err != nil {
		log.Fatal("Failed to execute seed data:", err)
	}

	fmt.Println("Database seeded successfully!")
}
