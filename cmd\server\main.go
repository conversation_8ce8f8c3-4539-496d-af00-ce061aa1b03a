package main

import (
	"log"
	"net/http"
	"os"
	"os/signal"
	"strconv"
	"syscall"

	"esign/internal/config"
	"esign/internal/database"
	"esign/internal/handler"
	"esign/internal/middleware"
	"esign/internal/repository"
	"esign/internal/service"

	"github.com/gin-gonic/gin"
)

func main() {
	// 加载配置
	cfg := config.Load()

	// 设置Gin模式
	if !cfg.Debug {
		gin.SetMode(gin.ReleaseMode)
	}

	// 初始化数据库
	dbConfig := &database.Config{
		DatabasePath: cfg.DatabasePath,
		LogLevel:     cfg.LogLevel,
	}

	db, err := database.NewDatabase(dbConfig)
	if err != nil {
		log.Fatal("Failed to initialize database:", err)
	}
	defer db.Close()

	// 自动迁移数据库表
	if err := db.AutoMigrate(); err != nil {
		log.Fatal("Failed to migrate database:", err)
	}

	// 数据库健康检查
	if err := db.HealthCheck(); err != nil {
		log.Fatal("Database health check failed:", err)
	}

	// 初始化仓储层
	templateRepo := repository.NewTemplateRepository(db.GetDB())
	signatureRepo := repository.NewSignatureRepository(db.GetDB())

	// 初始化服务层
	pdfService := service.NewPDFService()
	templateService := service.NewTemplateService(templateRepo)
	signatureService := service.NewSignatureService(signatureRepo, templateRepo, pdfService)

	// 初始化处理器
	adminHandler := handler.NewAdminHandler(templateService, signatureService)
	signHandler := handler.NewSignHandler(templateService, signatureService, pdfService)

	// 设置路由
	router := setupRoutes(adminHandler, signHandler, templateService, signatureService)

	// 启动服务器
	serverAddr := "10.16.0.10:" + cfg.Port
	log.Printf("Server starting on %s", serverAddr)
	log.Printf("Database path: %s", cfg.DatabasePath)
	log.Printf("Storage path: %s", cfg.StoragePath)

	// 优雅关闭
	go func() {
		if err := http.ListenAndServe(serverAddr, router); err != nil && err != http.ErrServerClosed {
			log.Fatal("Failed to start server:", err)
		}
	}()

	// 等待中断信号
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	log.Println("Server shutting down...")
}

func setupRoutes(adminHandler *handler.AdminHandler, signHandler *handler.SignHandler, templateService service.TemplateService, signatureService service.SignatureService) *gin.Engine {
	router := gin.New()

	// 添加中间件
	router.Use(middleware.Recovery())
	router.Use(middleware.RequestLogger())
	router.Use(middleware.SecurityHeaders())
	router.Use(middleware.CORS())
	router.Use(middleware.ErrorHandler())
	router.Use(middleware.MetricsMiddleware())
	router.Use(middleware.ResponseTime())
	router.Use(middleware.CacheControl(3600)) // 1小时缓存

	// 静态文件服务
	router.Static("/static", "./web/static")
	router.Static("/storage", "./storage")
	router.LoadHTMLFiles("web/templates/admin/index.html", "web/templates/mobile/sign.html", "web/templates/mobile/success.html")

	// API路由组
	api := router.Group("/api")
	api.Use(middleware.RateLimiter()) // 添加速率限制
	{
		// 后台管理API
		admin := api.Group("/admin")
		admin.Use(middleware.FileUploadSecurity()) // 文件上传安全
		{
			admin.POST("/templates", adminHandler.CreateTemplate)
			admin.GET("/templates", adminHandler.GetTemplates)
			admin.GET("/templates/:id", adminHandler.GetTemplate)
			admin.PUT("/templates/:id", adminHandler.UpdateTemplate)
			admin.DELETE("/templates/:id", adminHandler.DeleteTemplate)
			admin.POST("/templates/:id/qrcode", adminHandler.GenerateQRCode)
			admin.GET("/signatures", adminHandler.GetSignatures)
		}

		// 签署API
		sign := api.Group("/sign")
		{
			sign.GET("/:template_id", signHandler.GetSignPage)
			sign.POST("/:template_id", signHandler.SubmitSignature)
		}

		// 文档API
		api.GET("/preview/:signature_id", signHandler.PreviewDocument)
		api.GET("/download/:signature_id", signHandler.DownloadDocument)
	}

	// 监控和健康检查路由
	router.GET("/health", middleware.HealthCheck())
	router.GET("/metrics", middleware.MetricsHandler())

	// 页面路由
	router.GET("/admin", func(c *gin.Context) {
		c.HTML(http.StatusOK, "index.html", gin.H{
			"title": "电子签名管理后台",
		})
	})

	router.GET("/sign/:template_id", func(c *gin.Context) {
		templateID := c.Param("template_id")
		c.HTML(http.StatusOK, "sign.html", gin.H{
			"title":      "电子签名",
			"templateID": templateID,
		})
	})

	router.GET("/success/:signature_id", func(c *gin.Context) {
		signatureIDStr := c.Param("signature_id")
		signatureID, err := strconv.ParseUint(signatureIDStr, 10, 32)
		if err != nil {
			c.HTML(http.StatusBadRequest, "sign.html", gin.H{
				"title": "无效的签署ID",
				"error": "无效的签署ID",
			})
			return
		}

		// 获取签署记录详情
		signature, err := signatureService.GetSignature(uint(signatureID))
		if err != nil {
			c.HTML(http.StatusNotFound, "sign.html", gin.H{
				"title": "签署记录不存在",
				"error": "签署记录不存在",
			})
			return
		}

		// 获取模板信息
		template, err := templateService.GetTemplate(signature.TemplateID)
		if err != nil {
			c.HTML(http.StatusNotFound, "sign.html", gin.H{
				"title": "模板不存在",
				"error": "模板不存在",
			})
			return
		}

		c.HTML(http.StatusOK, "success.html", gin.H{
			"title":     "签署成功",
			"signature": signature,
			"template":  template,
		})
	})

	return router
}
