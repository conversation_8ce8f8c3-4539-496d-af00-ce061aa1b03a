<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试文件上传</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background-color: #0056b3;
        }
        .result {
            margin-top: 20px;
            padding: 10px;
            border-radius: 4px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <h1>电子签名模板上传测试</h1>
    
    <form id="uploadForm" enctype="multipart/form-data">
        <div class="form-group">
            <label for="name">模板名称:</label>
            <input type="text" id="name" name="name" required>
        </div>
        
        <div class="form-group">
            <label for="file">PDF文件:</label>
            <input type="file" id="file" name="file" accept=".pdf" required>
        </div>
        
        <div class="form-group">
            <label for="signature_x">签名X坐标:</label>
            <input type="number" id="signature_x" name="signature_x" step="0.1" value="150" required>
        </div>
        
        <div class="form-group">
            <label for="signature_y">签名Y坐标:</label>
            <input type="number" id="signature_y" name="signature_y" step="0.1" value="300" required>
        </div>
        
        <div class="form-group">
            <label for="signature_width">签名宽度:</label>
            <input type="number" id="signature_width" name="signature_width" step="0.1" value="120">
        </div>
        
        <div class="form-group">
            <label for="signature_height">签名高度:</label>
            <input type="number" id="signature_height" name="signature_height" step="0.1" value="60">
        </div>
        
        <button type="submit">上传模板</button>
    </form>
    
    <div id="result"></div>

    <script>
        document.getElementById('uploadForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const resultDiv = document.getElementById('result');
            
            try {
                const response = await fetch('http://localhost:8080/api/admin/templates', {
                    method: 'POST',
                    body: formData
                });
                
                const result = await response.json();
                
                if (response.ok) {
                    resultDiv.innerHTML = `
                        <div class="result success">
                            <h3>上传成功！</h3>
                            <p><strong>模板ID:</strong> ${result.id}</p>
                            <p><strong>模板名称:</strong> ${result.name}</p>
                            <p><strong>文件路径:</strong> ${result.file_path}</p>
                            <p><strong>签名位置:</strong> (${result.signature_x}, ${result.signature_y})</p>
                            <p><strong>创建时间:</strong> ${new Date(result.created_at).toLocaleString()}</p>
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="result error">
                            <h3>上传失败</h3>
                            <p>${result.error}</p>
                        </div>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="result error">
                        <h3>网络错误</h3>
                        <p>${error.message}</p>
                    </div>
                `;
            }
        });
    </script>
</body>
</html>
