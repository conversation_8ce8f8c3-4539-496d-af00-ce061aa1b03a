package model

import (
	"time"
)

// Signature 签署记录结构
type Signature struct {
	ID                 uint      `json:"id" gorm:"primaryKey"`
	TemplateID         uint      `json:"template_id" gorm:"not null"`
	SignerName         string    `json:"signer_name" gorm:"not null"`
	SignaturePath      string    `json:"signature_path" gorm:"not null"`
	SignedDocumentPath string    `json:"signed_document_path" gorm:"not null"`
	IPAddress          string    `json:"ip_address"`
	UserAgent          string    `json:"user_agent"`
	SignedAt           time.Time `json:"signed_at"`
	
	// 关联关系
	Template Template `json:"template" gorm:"foreignKey:TemplateID"`
}

// CreateSignatureRequest 创建签署请求
type CreateSignatureRequest struct {
	SignerName      string `json:"signer_name" binding:"required"`
	SignatureData   string `json:"signature_data" binding:"required"` // Base64编码的签名图片
}

// SignatureResponse 签署响应
type SignatureResponse struct {
	ID                 uint      `json:"id"`
	TemplateID         uint      `json:"template_id"`
	SignerName         string    `json:"signer_name"`
	SignedDocumentPath string    `json:"signed_document_path"`
	SignedAt           time.Time `json:"signed_at"`
}
