package repository

import (
	"time"

	"gorm.io/gorm"
)

// Signature 签署记录数据模型
type Signature struct {
	ID                 uint      `gorm:"primaryKey"`
	TemplateID         uint      `gorm:"not null"`
	SignerName         string    `gorm:"not null"`
	SignaturePath      string    `gorm:"not null"`
	SignedDocumentPath string    `gorm:"not null"`
	IPAddress          string
	UserAgent          string
	SignedAt           time.Time

	// 关联关系
	Template Template `gorm:"foreignKey:TemplateID"`
}

// SignatureRepository 签署记录仓储接口
type SignatureRepository interface {
	Create(signature *Signature) error
	GetByID(id uint) (*Signature, error)
	GetByTemplateID(templateID uint) ([]*Signature, error)
	GetAll() ([]*Signature, error)
	Delete(id uint) error
}

// signatureRepository 签署记录仓储实现
type signatureRepository struct {
	db *gorm.DB
}

// NewSignatureRepository 创建签署记录仓储实例
func NewSignatureRepository(db *gorm.DB) SignatureRepository {
	return &signatureRepository{db: db}
}

// Create 创建签署记录
func (r *signatureRepository) Create(signature *Signature) error {
	return r.db.Create(signature).Error
}

// GetByID 根据ID获取签署记录
func (r *signatureRepository) GetByID(id uint) (*Signature, error) {
	var signature Signature
	err := r.db.Preload("Template").First(&signature, id).Error
	if err != nil {
		return nil, err
	}
	return &signature, nil
}

// GetByTemplateID 根据模板ID获取签署记录
func (r *signatureRepository) GetByTemplateID(templateID uint) ([]*Signature, error) {
	var signatures []*Signature
	err := r.db.Preload("Template").Where("template_id = ?", templateID).Find(&signatures).Error
	return signatures, err
}

// GetAll 获取所有签署记录
func (r *signatureRepository) GetAll() ([]*Signature, error) {
	var signatures []*Signature
	err := r.db.Preload("Template").Order("signed_at DESC").Find(&signatures).Error
	return signatures, err
}

// Delete 删除签署记录
func (r *signatureRepository) Delete(id uint) error {
	return r.db.Delete(&Signature{}, id).Error
}
