package service

import (
	"errors"
	"testing"
	"time"

	"esign/internal/model"
	"esign/internal/repository"
)

// 测试用错误定义
var (
	ErrTemplateNotFound    = errors.New("template not found")
	ErrSignatureNotFound   = errors.New("signature not found")
	ErrPDFProcessingFailed = errors.New("PDF processing failed")
	ErrInvalidPDF          = errors.New("invalid PDF")
)

// MockTemplateRepository 模拟模板仓储
type MockTemplateRepository struct {
	templates map[uint]*repository.Template
}

func NewMockTemplateRepository() *MockTemplateRepository {
	return &MockTemplateRepository{
		templates: make(map[uint]*repository.Template),
	}
}

func (m *MockTemplateRepository) Create(template *repository.Template) error {
	template.ID = uint(len(m.templates) + 1)
	template.CreatedAt = time.Now()
	template.UpdatedAt = time.Now()
	m.templates[template.ID] = template
	return nil
}

func (m *MockTemplateRepository) GetByID(id uint) (*repository.Template, error) {
	if template, exists := m.templates[id]; exists {
		return template, nil
	}
	return nil, ErrTemplateNotFound
}

func (m *MockTemplateRepository) GetAll() ([]*repository.Template, error) {
	var templates []*repository.Template
	for _, template := range m.templates {
		templates = append(templates, template)
	}
	return templates, nil
}

func (m *MockTemplateRepository) Update(template *repository.Template) error {
	if _, exists := m.templates[template.ID]; exists {
		template.UpdatedAt = time.Now()
		m.templates[template.ID] = template
		return nil
	}
	return ErrTemplateNotFound
}

func (m *MockTemplateRepository) Delete(id uint) error {
	if _, exists := m.templates[id]; exists {
		delete(m.templates, id)
		return nil
	}
	return ErrTemplateNotFound
}

// MockSignatureRepository 模拟签署仓储
type MockSignatureRepository struct {
	signatures map[uint]*repository.Signature
}

func NewMockSignatureRepository() *MockSignatureRepository {
	return &MockSignatureRepository{
		signatures: make(map[uint]*repository.Signature),
	}
}

func (m *MockSignatureRepository) Create(signature *repository.Signature) error {
	signature.ID = uint(len(m.signatures) + 1)
	signature.SignedAt = time.Now()
	m.signatures[signature.ID] = signature
	return nil
}

func (m *MockSignatureRepository) GetByID(id uint) (*repository.Signature, error) {
	if signature, exists := m.signatures[id]; exists {
		return signature, nil
	}
	return nil, ErrSignatureNotFound
}

func (m *MockSignatureRepository) GetByTemplateID(templateID uint) ([]*repository.Signature, error) {
	var signatures []*repository.Signature
	for _, signature := range m.signatures {
		if signature.TemplateID == templateID {
			signatures = append(signatures, signature)
		}
	}
	return signatures, nil
}

func (m *MockSignatureRepository) GetAll() ([]*repository.Signature, error) {
	var signatures []*repository.Signature
	for _, signature := range m.signatures {
		signatures = append(signatures, signature)
	}
	return signatures, nil
}

func (m *MockSignatureRepository) Delete(id uint) error {
	if _, exists := m.signatures[id]; exists {
		delete(m.signatures, id)
		return nil
	}
	return ErrSignatureNotFound
}

// MockPDFService 模拟PDF服务
type MockPDFService struct {
	shouldFail bool
}

func NewMockPDFService(shouldFail bool) *MockPDFService {
	return &MockPDFService{shouldFail: shouldFail}
}

func (m *MockPDFService) ProcessSignedDocument(templatePath, signaturePath, outputPath string, x, y, width, height float64) error {
	if m.shouldFail {
		return ErrPDFProcessingFailed
	}
	return nil
}

func (m *MockPDFService) ValidatePDF(filePath string) error {
	if m.shouldFail {
		return ErrInvalidPDF
	}
	return nil
}

func (m *MockPDFService) GetPDFInfo(filePath string) (*PDFInfo, error) {
	if m.shouldFail {
		return nil, ErrInvalidPDF
	}
	return &PDFInfo{
		PageCount: 1,
		Width:     595,
		Height:    842,
		Title:     "Test PDF",
	}, nil
}

func (m *MockPDFService) CreateSignedPDF(templatePath, signaturePath, outputPath string, signatureConfig *SignatureConfig) error {
	if m.shouldFail {
		return ErrPDFProcessingFailed
	}
	return nil
}

// TestSignatureService_CreateSignature 测试创建签署记录
func TestSignatureService_CreateSignature(t *testing.T) {
	// 准备测试数据
	templateRepo := NewMockTemplateRepository()
	signatureRepo := NewMockSignatureRepository()
	pdfService := NewMockPDFService(false)

	// 创建测试模板
	template := &repository.Template{
		Name:            "Test Template",
		FilePath:        "test.pdf",
		SignatureX:      100,
		SignatureY:      200,
		SignatureWidth:  120,
		SignatureHeight: 60,
	}
	templateRepo.Create(template)

	// 创建签署服务
	service := NewSignatureService(signatureRepo, templateRepo, pdfService)

	// 测试数据 - 使用有效的Base64编码的1x1像素PNG图片
	req := &model.CreateSignatureRequest{
		SignerName:    "Test User",
		SignatureData: "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==",
	}

	// 执行测试
	result, err := service.CreateSignature(template.ID, req, "127.0.0.1", "test-agent")

	// 验证结果
	if err != nil {
		t.Fatalf("Expected no error, got %v", err)
	}

	if result.SignerName != req.SignerName {
		t.Errorf("Expected signer name %s, got %s", req.SignerName, result.SignerName)
	}

	if result.TemplateID != template.ID {
		t.Errorf("Expected template ID %d, got %d", template.ID, result.TemplateID)
	}
}

// TestSignatureService_CreateSignature_TemplateNotFound 测试模板不存在的情况
func TestSignatureService_CreateSignature_TemplateNotFound(t *testing.T) {
	templateRepo := NewMockTemplateRepository()
	signatureRepo := NewMockSignatureRepository()
	pdfService := NewMockPDFService(false)

	service := NewSignatureService(signatureRepo, templateRepo, pdfService)

	req := &model.CreateSignatureRequest{
		SignerName:    "Test User",
		SignatureData: "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==",
	}

	// 使用不存在的模板ID
	_, err := service.CreateSignature(999, req, "127.0.0.1", "test-agent")

	if err == nil {
		t.Fatal("Expected error for non-existent template, got nil")
	}
}
