// 后台管理JavaScript
document.addEventListener('DOMContentLoaded', function() {
    // 初始化页面
    loadTemplates();
    loadSignatures();
    
    // 绑定事件
    bindEvents();
});

function bindEvents() {
    // 上传按钮点击事件
    document.getElementById('uploadBtn').addEventListener('click', function() {
        document.getElementById('uploadModal').style.display = 'block';
    });
    
    // 关闭模态框
    document.querySelector('.close').addEventListener('click', closeModal);
    
    // 点击模态框外部关闭
    window.addEventListener('click', function(event) {
        const modal = document.getElementById('uploadModal');
        if (event.target === modal) {
            closeModal();
        }
    });
    
    // 表单提交
    document.getElementById('uploadForm').addEventListener('submit', handleUpload);
}

function closeModal() {
    document.getElementById('uploadModal').style.display = 'none';
    document.getElementById('uploadForm').reset();
}

async function handleUpload(event) {
    event.preventDefault();

    const form = event.target;
    const formData = new FormData();

    // 添加表单字段到FormData
    formData.append('name', form.name.value);
    formData.append('signature_x', form.signature_x.value);
    formData.append('signature_y', form.signature_y.value);
    formData.append('signature_width', form.signature_width.value || '100');
    formData.append('signature_height', form.signature_height.value || '50');

    // 添加文件
    const fileInput = form.file;
    if (!fileInput.files[0]) {
        showMessage('请选择PDF文件', 'error');
        return;
    }
    formData.append('file', fileInput.files[0]);

    try {
        const response = await fetch('/api/admin/templates', {
            method: 'POST',
            body: formData
        });

        if (response.ok) {
            const result = await response.json();
            showMessage('模板创建成功！', 'success');
            closeModal();
            loadTemplates(); // 重新加载模板列表
        } else {
            const error = await response.json();
            showMessage('创建失败: ' + error.error, 'error');
        }
    } catch (error) {
        showMessage('网络错误: ' + error.message, 'error');
    }
}

async function loadTemplates() {
    try {
        const response = await fetch('/api/admin/templates');
        if (response.ok) {
            const templates = await response.json();
            renderTemplates(templates);
        } else {
            showMessage('加载模板失败', 'error');
        }
    } catch (error) {
        showMessage('网络错误: ' + error.message, 'error');
    }
}

function renderTemplates(templates) {
    const container = document.getElementById('templateList');
    
    if (!templates || templates.length === 0) {
        container.innerHTML = '<p>暂无模板</p>';
        return;
    }
    
    const html = templates.map(template => `
        <div class="list-item">
            <div class="item-info">
                <h4>${template.name}</h4>
                <p>创建时间: ${new Date(template.created_at).toLocaleString()}</p>
                <p>签名位置: (${template.signature_x}, ${template.signature_y})</p>
                <p>文件路径: ${template.file_path}</p>
                ${template.qr_code_path ? `<p>二维码: <a href="/${template.qr_code_path}" target="_blank">查看</a></p>` : ''}
            </div>
            <div class="item-actions">
                <button class="btn btn-secondary" onclick="generateQRCode(${template.id})">
                    ${template.qr_code_path ? '重新生成二维码' : '生成二维码'}
                </button>
                <button class="btn btn-primary" onclick="editTemplate(${template.id})">编辑</button>
                <button class="btn btn-danger" onclick="deleteTemplate(${template.id})">删除</button>
            </div>
        </div>
    `).join('');
    
    container.innerHTML = html;
}

async function loadSignatures() {
    try {
        const response = await fetch('/api/admin/signatures');
        if (response.ok) {
            const signatures = await response.json();
            renderSignatures(signatures);
        } else {
            showMessage('加载签署记录失败', 'error');
        }
    } catch (error) {
        showMessage('网络错误: ' + error.message, 'error');
    }
}

function renderSignatures(signatures) {
    const container = document.getElementById('signatureList');
    
    if (!signatures || signatures.length === 0) {
        container.innerHTML = '<p>暂无签署记录</p>';
        return;
    }
    
    const html = signatures.map(signature => `
        <div class="list-item">
            <div class="item-info">
                <h4>${signature.signer_name}</h4>
                <p>模板: ${signature.template.name}</p>
                <p>签署时间: ${new Date(signature.signed_at).toLocaleString()}</p>
                <p>IP地址: ${signature.ip_address}</p>
            </div>
            <div class="item-actions">
                <button class="btn btn-primary" onclick="previewDocument(${signature.id})">预览文档</button>
            </div>
        </div>
    `).join('');
    
    container.innerHTML = html;
}

async function deleteTemplate(id) {
    if (!confirm('确定要删除这个模板吗？')) {
        return;
    }
    
    try {
        const response = await fetch(`/api/admin/templates/${id}`, {
            method: 'DELETE'
        });
        
        if (response.ok) {
            showMessage('模板删除成功', 'success');
            loadTemplates();
        } else {
            const error = await response.json();
            showMessage('删除失败: ' + error.error, 'error');
        }
    } catch (error) {
        showMessage('网络错误: ' + error.message, 'error');
    }
}

async function generateQRCode(templateId) {
    try {
        const response = await fetch(`/api/admin/templates/${templateId}/qrcode`, {
            method: 'POST'
        });

        if (response.ok) {
            const result = await response.json();
            showMessage('二维码生成成功！', 'success');
            loadTemplates(); // 重新加载模板列表以显示二维码路径
        } else {
            const error = await response.json();
            showMessage('二维码生成失败: ' + error.error, 'error');
        }
    } catch (error) {
        showMessage('网络错误: ' + error.message, 'error');
    }
}

function editTemplate(templateId) {
    // TODO: 实现模板编辑
    showMessage('模板编辑功能待实现', 'info');
}

function previewDocument(signatureId) {
    // 打开新窗口预览文档
    window.open(`/api/preview/${signatureId}`, '_blank');
}

function showMessage(message, type = 'info') {
    // 创建消息元素
    const messageEl = document.createElement('div');
    messageEl.className = `message message-${type}`;
    messageEl.textContent = message;
    
    // 添加样式
    messageEl.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 12px 20px;
        border-radius: 4px;
        color: white;
        z-index: 1001;
        max-width: 300px;
    `;
    
    // 根据类型设置背景色
    switch (type) {
        case 'success':
            messageEl.style.backgroundColor = '#27ae60';
            break;
        case 'error':
            messageEl.style.backgroundColor = '#e74c3c';
            break;
        case 'warning':
            messageEl.style.backgroundColor = '#f39c12';
            break;
        default:
            messageEl.style.backgroundColor = '#3498db';
    }
    
    // 添加到页面
    document.body.appendChild(messageEl);
    
    // 3秒后自动移除
    setTimeout(() => {
        if (messageEl.parentNode) {
            messageEl.parentNode.removeChild(messageEl);
        }
    }, 3000);
}
