# 电子签名应用技术方案

## 项目概述

基于Go语言和SQLite数据库构建的电子签名应用，支持后台PDF模板管理、移动端扫码签署、文档预览等功能。

## 1. 技术栈选择

### 后端技术栈
- **Go 1.21+**: 主要开发语言
- **SQLite3**: 数据持久化
- **Gin**: HTTP Web框架（轻量级，适合API开发）
- **GORM**: ORM框架（简化数据库操作）

### PDF处理
- **gofpdf**: PDF生成和操作
- **unidoc/unipdf**: PDF文档处理（支持更复杂的PDF操作）

### 二维码生成
- **go-qrcode**: 二维码生成库

### 前端技术栈
- **HTML5 + CSS3 + JavaScript**: 移动端页面
- **Canvas API**: 手写签名功能
- **响应式设计**: 移动端适配

## 2. 系统架构设计

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   后台管理端     │    │   移动端签署     │    │   数据存储       │
│                │    │                │    │                │
│ - 模板上传      │    │ - 扫码进入      │    │ - SQLite数据库   │
│ - 签名位置配置   │    │ - 姓名输入      │    │ - PDF文件存储    │
│ - 二维码生成    │    │ - 手写签名      │    │ - 签名图片存储   │
│ - 签署记录查看   │    │ - 文档预览      │    │                │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                │
                    ┌─────────────────┐
                    │   Go后端服务     │
                    │                │
                    │ - RESTful API  │
                    │ - PDF处理      │
                    │ - 文件管理      │
                    │ - 业务逻辑      │
                    └─────────────────┘
```

## 3. 数据库设计

### 模板表 (templates)
```sql
CREATE TABLE templates (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    signature_x FLOAT NOT NULL,
    signature_y FLOAT NOT NULL,
    signature_width FLOAT DEFAULT 100,
    signature_height FLOAT DEFAULT 50,
    qr_code_path VARCHAR(500),
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

### 签署记录表 (signatures)
```sql
CREATE TABLE signatures (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    template_id INTEGER NOT NULL,
    signer_name VARCHAR(100) NOT NULL,
    signature_path VARCHAR(500) NOT NULL,
    signed_document_path VARCHAR(500) NOT NULL,
    ip_address VARCHAR(45),
    user_agent TEXT,
    signed_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (template_id) REFERENCES templates(id)
);
```

## 4. API接口设计

### 后台管理API
```
POST   /api/admin/templates          # 上传PDF模板
PUT    /api/admin/templates/:id      # 更新模板配置
GET    /api/admin/templates          # 获取模板列表
DELETE /api/admin/templates/:id      # 删除模板
GET    /api/admin/signatures         # 获取签署记录
```

### 移动端签署API
```
GET    /api/sign/:template_id        # 获取签署页面
POST   /api/sign/:template_id        # 提交签名
GET    /api/preview/:signature_id    # 预览签署后文档
```

## 5. 核心功能模块

### 1. 模板管理模块
- PDF文件上传和存储
- 签名位置坐标配置
- 二维码生成和关联
- 模板信息CRUD操作

### 2. 签署流程模块
- 扫码跳转到签署页面
- 姓名输入验证
- Canvas手写签名
- 签名图片生成和保存
- PDF文档合成

### 3. 文档处理模块
- PDF文件读取和解析
- 签名图片嵌入PDF
- 签署后文档生成
- 文档预览功能

### 4. 数据持久化模块
- SQLite数据库操作
- 文件系统管理
- 签署记录存储

## 6. 目录结构设计

```
eSign/
├── cmd/
│   └── server/
│       └── main.go              # 应用入口
├── internal/
│   ├── config/                  # 配置管理
│   ├── handler/                 # HTTP处理器
│   │   ├── admin.go            # 后台管理接口
│   │   └── sign.go             # 签署接口
│   ├── model/                   # 数据模型
│   │   ├── template.go
│   │   └── signature.go
│   ├── service/                 # 业务逻辑
│   │   ├── template.go
│   │   ├── signature.go
│   │   └── pdf.go
│   ├── repository/              # 数据访问层
│   └── middleware/              # 中间件
├── web/
│   ├── static/                  # 静态资源
│   │   ├── css/
│   │   ├── js/
│   │   └── images/
│   └── templates/               # HTML模板
│       ├── admin/
│       └── mobile/
├── storage/
│   ├── templates/               # PDF模板存储
│   ├── signatures/              # 签名图片存储
│   ├── documents/               # 签署后文档存储
│   └── qrcodes/                # 二维码存储
├── database/
│   └── esign.db                # SQLite数据库文件
├── go.mod
├── go.sum
└── README.md
```

## 7. 关键技术实现要点

### 1. PDF处理
- 使用unipdf库读取PDF文档
- 计算签名位置的绝对坐标
- 将Canvas签名转换为图片格式
- 在指定位置嵌入签名图片

### 2. 移动端适配
- 使用viewport meta标签
- CSS媒体查询适配不同屏幕
- 强制横屏显示签名界面
- Touch事件处理手写签名

### 3. 二维码集成
- 生成包含模板ID的签署链接
- 二维码图片存储和管理
- 扫码后的参数解析和验证

### 4. 安全考虑
- 文件上传类型验证
- SQL注入防护
- XSS攻击防护
- 文件访问权限控制

## 8. 实施计划

### 阶段一：基础架构搭建
1. 项目结构创建
2. Go模块初始化
3. 依赖包安装
4. 数据库初始化

### 阶段二：核心功能开发
1. PDF处理模块
2. 数据模型和仓储层
3. 业务逻辑服务层
4. HTTP接口层

### 阶段三：前端界面开发
1. 后台管理界面
2. 移动端签署界面
3. 响应式样式适配

### 阶段四：集成测试
1. 单元测试编写
2. 集成测试
3. 性能优化
4. 部署配置

## 9. 技术难点和解决方案

### PDF坐标定位
- 问题：PDF坐标系与Web坐标系差异
- 解决：建立坐标转换算法，支持不同PDF尺寸

### 移动端签名体验
- 问题：触摸签名的流畅性和准确性
- 解决：优化Canvas事件处理，添加防抖和平滑算法

### 文件存储管理
- 问题：大量PDF和签名文件的存储组织
- 解决：按日期和类型分目录存储，定期清理临时文件

### 并发安全
- 问题：多用户同时签署同一模板
- 解决：使用文件锁和数据库事务保证数据一致性

## 10. 性能优化策略

1. **文件缓存**: 对常用PDF模板进行内存缓存
2. **图片压缩**: 签名图片自动压缩减少存储空间
3. **数据库索引**: 为查询频繁的字段添加索引
4. **静态资源**: 使用CDN或本地缓存静态文件
5. **连接池**: 配置数据库连接池提高并发性能

---

*文档版本: v1.0*  
*创建时间: 2025-08-10*  
*技术栈: Go + SQLite + Gin + GORM*
