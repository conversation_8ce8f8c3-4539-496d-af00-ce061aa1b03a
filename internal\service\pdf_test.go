package service

import (
	"os"
	"testing"
)

// TestPDFService_ValidatePDF 测试PDF验证功能
func TestPDFService_ValidatePDF(t *testing.T) {
	service := NewPDFService()

	// 测试不存在的文件
	err := service.ValidatePDF("nonexistent.pdf")
	if err == nil {
		t.Error("Expected error for non-existent file, got nil")
	}

	// 创建一个临时的非PDF文件
	tempFile := "test_temp.txt"
	file, err := os.Create(tempFile)
	if err != nil {
		t.Fatalf("Failed to create temp file: %v", err)
	}
	file.WriteString("This is not a PDF file")
	file.Close()
	defer os.Remove(tempFile)

	// 测试非PDF文件
	err = service.ValidatePDF(tempFile)
	if err == nil {
		t.Error("Expected error for non-PDF file, got nil")
	}
}

// TestSignatureConfig 测试签名配置
func TestSignatureConfig(t *testing.T) {
	config := &SignatureConfig{
		X:      100.0,
		Y:      200.0,
		Width:  120.0,
		Height: 60.0,
		PageNo: 1,
	}

	if config.X != 100.0 {
		t.Errorf("Expected X to be 100.0, got %f", config.X)
	}

	if config.Y != 200.0 {
		t.Errorf("Expected Y to be 200.0, got %f", config.Y)
	}

	if config.Width != 120.0 {
		t.Errorf("Expected Width to be 120.0, got %f", config.Width)
	}

	if config.Height != 60.0 {
		t.Errorf("Expected Height to be 60.0, got %f", config.Height)
	}

	if config.PageNo != 1 {
		t.Errorf("Expected PageNo to be 1, got %d", config.PageNo)
	}
}

// TestPDFInfo 测试PDF信息结构
func TestPDFInfo(t *testing.T) {
	info := &PDFInfo{
		PageCount: 5,
		Width:     595.0,
		Height:    842.0,
		Title:     "Test Document",
	}

	if info.PageCount != 5 {
		t.Errorf("Expected PageCount to be 5, got %d", info.PageCount)
	}

	if info.Width != 595.0 {
		t.Errorf("Expected Width to be 595.0, got %f", info.Width)
	}

	if info.Height != 842.0 {
		t.Errorf("Expected Height to be 842.0, got %f", info.Height)
	}

	if info.Title != "Test Document" {
		t.Errorf("Expected Title to be 'Test Document', got %s", info.Title)
	}
}

// TestPDFService_ProcessSignedDocument 测试PDF处理功能
func TestPDFService_ProcessSignedDocument(t *testing.T) {
	service := NewPDFService()

	// 测试不存在的模板文件
	err := service.ProcessSignedDocument(
		"nonexistent_template.pdf",
		"nonexistent_signature.png",
		"output.pdf",
		100, 200, 120, 60,
	)

	if err == nil {
		t.Error("Expected error for non-existent template file, got nil")
	}
}

// TestPDFService_CreateSignedPDF 测试创建签署后PDF
func TestPDFService_CreateSignedPDF(t *testing.T) {
	service := NewPDFService()

	config := &SignatureConfig{
		X:      100.0,
		Y:      200.0,
		Width:  120.0,
		Height: 60.0,
		PageNo: 1,
	}

	// 测试不存在的模板文件
	err := service.CreateSignedPDF(
		"nonexistent_template.pdf",
		"nonexistent_signature.png",
		"output.pdf",
		config,
	)

	if err == nil {
		t.Error("Expected error for non-existent template file, got nil")
	}
}
