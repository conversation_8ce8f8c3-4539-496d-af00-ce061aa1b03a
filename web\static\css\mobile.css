/* 移动端签署样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Microsoft YaHei', Arial, sans-serif;
    background-color: #f5f5f5;
    color: #333;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

.container {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

header {
    background: #3498db;
    color: white;
    padding: 15px;
    text-align: center;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

header h1 {
    font-size: 18px;
}

main {
    flex: 1;
    padding: 20px;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.step {
    display: none;
    text-align: center;
}

.step.active {
    display: block;
}

.step h2 {
    color: #2c3e50;
    margin-bottom: 30px;
    font-size: 20px;
}

/* 姓名输入步骤 */
.form-group {
    margin-bottom: 30px;
}

.form-group input {
    width: 100%;
    max-width: 300px;
    padding: 15px;
    border: 2px solid #ddd;
    border-radius: 8px;
    font-size: 16px;
    text-align: center;
}

.form-group input:focus {
    outline: none;
    border-color: #3498db;
}

/* 签名步骤 */
.signature-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 20px;
}

#signatureCanvas {
    border: 2px solid #ddd;
    border-radius: 8px;
    background: white;
    cursor: crosshair;
    max-width: 100%;
    height: auto;
}

.signature-actions {
    display: flex;
    gap: 15px;
}

.tip {
    color: #7f8c8d;
    font-size: 14px;
    margin-top: 10px;
}

/* 预览步骤 */
.preview-container {
    text-align: center;
}

.preview-container p {
    font-size: 18px;
    color: #27ae60;
    margin-bottom: 20px;
}

#documentPreview {
    background: white;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 20px;
    margin-top: 20px;
    min-height: 200px;
}

.preview-info {
    text-align: left;
}

.preview-info h3 {
    color: #27ae60;
    margin-bottom: 15px;
    text-align: center;
}

.preview-info p {
    margin: 8px 0;
    font-size: 14px;
    color: #333;
}

.preview-actions {
    margin-top: 20px;
    display: flex;
    gap: 10px;
    justify-content: center;
    flex-wrap: wrap;
}

.preview-actions .btn {
    min-width: 120px;
}

/* 按钮样式 */
.btn {
    padding: 12px 24px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 16px;
    font-weight: bold;
    transition: all 0.3s;
    min-width: 100px;
}

.btn-primary {
    background-color: #3498db;
    color: white;
}

.btn-primary:hover {
    background-color: #2980b9;
    transform: translateY(-1px);
}

.btn-secondary {
    background-color: #95a5a6;
    color: white;
}

.btn-secondary:hover {
    background-color: #7f8c8d;
    transform: translateY(-1px);
}

.btn:disabled {
    background-color: #bdc3c7;
    cursor: not-allowed;
    transform: none;
}

/* 横屏适配 */
@media screen and (orientation: landscape) {
    .signature-container {
        flex-direction: row;
        justify-content: center;
        align-items: flex-start;
    }
    
    #signatureCanvas {
        width: 70vw;
        height: 40vh;
    }
    
    .signature-actions {
        flex-direction: column;
        margin-left: 20px;
    }
    
    .tip {
        display: none;
    }
}

/* 竖屏提示 */
@media screen and (orientation: portrait) {
    .landscape-tip {
        display: block;
        background: #f39c12;
        color: white;
        padding: 10px;
        text-align: center;
        font-size: 14px;
    }
}

.landscape-tip {
    display: none;
}

/* 加载动画 */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 错误提示 */
.error-message {
    background: #e74c3c;
    color: white;
    padding: 10px;
    border-radius: 4px;
    margin: 10px 0;
    text-align: center;
}

.success-message {
    background: #27ae60;
    color: white;
    padding: 10px;
    border-radius: 4px;
    margin: 10px 0;
    text-align: center;
}

.warning-message {
    background: #f39c12;
    color: white;
    padding: 10px;
    border-radius: 4px;
    margin: 10px 0;
    text-align: center;
}

/* 签名质量提示 */
.signature-tips {
    background: #e8f4fd;
    border: 1px solid #bee5eb;
    border-radius: 4px;
    padding: 10px;
    margin: 10px 0;
    font-size: 12px;
    color: #0c5460;
}

/* 文档预览样式增强 */
.document-status {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: bold;
}

.status-success {
    background: #d4edda;
    color: #155724;
}

.status-processing {
    background: #fff3cd;
    color: #856404;
}

.status-error {
    background: #f8d7da;
    color: #721c24;
}
