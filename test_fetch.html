<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fetch测试</title>
</head>
<body>
    <h1>Fetch API 测试</h1>
    <button onclick="testFetch()">测试签名API</button>
    <div id="result"></div>

    <script>
        async function testFetch() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '测试中...';

            const requestData = {
                signer_name: "Fetch测试用户",
                signature_data: "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg=="
            };

            try {
                console.log('开始测试fetch请求...');
                console.log('请求URL:', 'http://**********:8080/api/sign/4');
                console.log('请求数据:', requestData);

                const response = await fetch('http://**********:8080/api/sign/4', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify(requestData),
                    mode: 'cors',
                    credentials: 'include'
                });

                console.log('响应状态:', response.status);
                console.log('响应头:', response.headers);

                if (response.ok) {
                    const result = await response.json();
                    console.log('响应数据:', result);
                    resultDiv.innerHTML = `
                        <h3>✅ 成功!</h3>
                        <p>签署ID: ${result.signature.id}</p>
                        <p>签署人: ${result.signature.signer_name}</p>
                        <p>消息: ${result.message}</p>
                    `;
                } else {
                    const error = await response.text();
                    console.error('请求失败:', error);
                    resultDiv.innerHTML = `
                        <h3>❌ 失败!</h3>
                        <p>状态码: ${response.status}</p>
                        <p>错误: ${error}</p>
                    `;
                }
            } catch (error) {
                console.error('Fetch错误:', error);
                resultDiv.innerHTML = `
                    <h3>❌ 网络错误!</h3>
                    <p>错误类型: ${error.name}</p>
                    <p>错误消息: ${error.message}</p>
                    <p>错误堆栈: ${error.stack}</p>
                `;
            }
        }

        // 页面加载时显示当前URL和环境信息
        window.addEventListener('load', function() {
            console.log('当前页面URL:', window.location.href);
            console.log('User Agent:', navigator.userAgent);
            console.log('是否支持fetch:', typeof fetch !== 'undefined');
        });
    </script>
</body>
</html>
