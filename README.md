# 电子签名应用 (eSign)

基于Go语言和SQLite数据库构建的电子签名应用，支持PDF模板管理、移动端扫码签署、文档预览等功能。

## 功能特性

- ✅ **后台模板管理**: PDF文件上传、签名位置配置、二维码生成
- ✅ **移动端签署**: 扫码进入、姓名输入、手写签名、文档预览
- ✅ **数据持久化**: SQLite数据库存储模板和签署记录
- ✅ **响应式设计**: 移动端优化，支持横屏签名
- ✅ **RESTful API**: 标准化接口设计

## 技术栈

- **后端**: Go 1.21+, Gin, GORM, SQLite3
- **前端**: HTML5, CSS3, JavaScript, Canvas API
- **PDF处理**: unipdf (待实现)
- **二维码**: go-qrcode

## 项目结构

```
eSign/
├── cmd/server/main.go           # 应用入口
├── internal/                    # 内部模块
│   ├── config/                  # 配置管理
│   ├── handler/                 # HTTP处理器
│   ├── model/                   # 数据模型
│   ├── service/                 # 业务逻辑
│   ├── repository/              # 数据访问层
│   └── middleware/              # 中间件
├── web/                         # Web资源
│   ├── static/                  # 静态文件
│   └── templates/               # HTML模板
├── storage/                     # 文件存储
│   ├── templates/               # PDF模板
│   ├── signatures/              # 签名图片
│   ├── documents/               # 签署后文档
│   └── qrcodes/                # 二维码
└── database/                    # 数据库文件
```

## 快速开始

### 1. 环境要求

- Go 1.21 或更高版本
- Git

### 2. 安装依赖

```bash
go mod tidy
```

### 3. 运行应用

```bash
go run cmd/server/main.go
```

### 4. 访问应用

- 后台管理: http://localhost:8080/admin
- API文档: http://localhost:8080/api

## API接口

### 后台管理API

| 方法 | 路径 | 描述 |
|------|------|------|
| POST | /api/admin/templates | 创建PDF模板 |
| GET | /api/admin/templates | 获取模板列表 |
| PUT | /api/admin/templates/:id | 更新模板配置 |
| DELETE | /api/admin/templates/:id | 删除模板 |
| GET | /api/admin/signatures | 获取签署记录 |

### 签署API

| 方法 | 路径 | 描述 |
|------|------|------|
| GET | /api/sign/:template_id | 获取签署页面信息 |
| POST | /api/sign/:template_id | 提交签名 |
| GET | /api/preview/:signature_id | 预览签署后文档 |

## 配置说明

应用支持通过环境变量进行配置：

| 环境变量 | 默认值 | 描述 |
|----------|--------|------|
| PORT | 8080 | 服务端口 |
| DATABASE_PATH | ./database/esign.db | 数据库文件路径 |
| STORAGE_PATH | ./storage | 文件存储路径 |
| BASE_URL | http://localhost:8080 | 应用基础URL |

## 开发计划

### 已完成 ✅
- [x] 项目基础架构搭建
- [x] 数据库模型设计
- [x] RESTful API框架
- [x] 基础前端界面
- [x] 配置管理系统

### 进行中 🚧
- [ ] PDF处理模块实现
- [ ] 文件上传功能
- [ ] 移动端签名JavaScript
- [ ] 二维码生成集成

### 待开发 📋
- [ ] PDF签名嵌入
- [ ] 文档预览功能
- [ ] 单元测试编写
- [ ] 性能优化
- [ ] 部署文档

## 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 联系方式

如有问题或建议，请通过以下方式联系：

- 提交 Issue
- 发送邮件
- 微信群讨论

---

*项目版本: v0.1.0*  
*最后更新: 2025-08-10*
