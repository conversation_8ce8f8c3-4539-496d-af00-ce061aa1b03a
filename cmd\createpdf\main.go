package main

import (
	"fmt"
	"os"

	"github.com/jung-kurt/gofpdf"
)

func main() {
	// 创建PDF文档
	pdf := gofpdf.New("P", "pt", "A4", "")
	pdf.AddPage()

	// 设置字体
	pdf.SetFont("Arial", "B", 24)

	// 添加标题
	pdf.Cell(0, 30, "Labor Contract")
	pdf.Ln(40)

	// 设置正文字体
	pdf.SetFont("Arial", "", 12)

	// 添加合同内容
	content := []string{
		"Party A: Company Name",
		"Party B: Employee Name",
		"",
		"According to the Labor Law of the People's Republic of China and other relevant laws and regulations,",
		"Party A and Party B, on the basis of equality, voluntariness and consensus, sign this labor contract.",
		"",
		"Article 1: Work Content and Workplace",
		"Party B agrees to work in the position arranged by Party A according to the work needs of Party A.",
		"",
		"Article 2: Term of Labor Contract",
		"This contract is a fixed-term labor contract with a term of three years.",
		"",
		"Article 3: Working Hours and Rest",
		"Party A arranges Party B to implement the standard working hour system.",
		"",
		"Article 4: Labor Remuneration",
		"Party B's monthly salary is RMB _____ yuan.",
		"",
		"Article 5: Social Insurance",
		"Party A shall pay social insurance premiums for Party B in accordance with the law.",
		"",
		"Article 6: Labor Discipline",
		"Party B shall abide by the rules and regulations legally formulated by Party A.",
		"",
		"Article 7: Contract Change, Termination and Termination",
		"Implemented in accordance with relevant laws and regulations.",
		"",
		"Article 8: Liability for Breach of Contract",
		"Any party that violates the provisions of this contract shall bear corresponding liability for breach of contract.",
		"",
		"Article 9: Dispute Resolution",
		"Disputes arising from the performance of this contract shall be resolved through consultation between the two parties.",
		"",
		"Article 10: Others",
		"This contract is made in duplicate, with each party holding one copy.",
		"",
		"",
		"Party A (Seal): ________________    Party B (Signature): ________________",
		"",
		"",
		"Date: ________________           Date: ________________",
	}

	// 添加每行内容
	for _, line := range content {
		if line == "" {
			pdf.Ln(15)
		} else {
			pdf.Cell(0, 15, line)
			pdf.Ln(18)
		}
	}

	// 确保目录存在
	if err := os.MkdirAll("storage/templates", 0755); err != nil {
		fmt.Printf("Failed to create directory: %v\n", err)
		os.Exit(1)
	}

	// 保存PDF
	err := pdf.OutputFileAndClose("storage/templates/labor_contract.pdf")
	if err != nil {
		fmt.Printf("Failed to create PDF: %v\n", err)
		os.Exit(1)
	}

	fmt.Println("✅ PDF file created successfully: storage/templates/labor_contract.pdf")
}
