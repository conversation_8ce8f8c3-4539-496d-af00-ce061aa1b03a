-- 电子签名应用种子数据
-- 用于开发和测试环境

-- 插入示例模板数据
INSERT OR IGNORE INTO templates (
    id, name, file_path, signature_x, signature_y, signature_width, signature_height, 
    created_at, updated_at
) VALUES 
(1, '劳动合同模板', 'storage/templates/labor_contract.pdf', 150.0, 300.0, 120.0, 60.0, 
 datetime('now'), datetime('now')),
(2, '保密协议模板', 'storage/templates/nda_agreement.pdf', 200.0, 250.0, 100.0, 50.0, 
 datetime('now'), datetime('now')),
(3, '租赁合同模板', 'storage/templates/lease_contract.pdf', 180.0, 400.0, 110.0, 55.0, 
 datetime('now'), datetime('now'));

-- 插入示例签署记录（可选，用于测试）
INSERT OR IGNORE INTO signatures (
    id, template_id, signer_name, signature_path, signed_document_path, 
    ip_address, user_agent, signed_at
) VALUES 
(1, 1, '张三', 'storage/signatures/signature_1_张三_20250810120000.png', 
 'storage/documents/signed_1_张三_20250810120000.pdf', '*************', 
 'Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X)', datetime('now', '-1 day')),
(2, 2, '李四', 'storage/signatures/signature_2_李四_20250810130000.png', 
 'storage/documents/signed_2_李四_20250810130000.pdf', '*************', 
 'Mozilla/5.0 (Android 12; Mobile)', datetime('now', '-2 hours')),
(3, 1, '王五', 'storage/signatures/signature_1_王五_20250810140000.png', 
 'storage/documents/signed_1_王五_20250810140000.pdf', '*************', 
 'Mozilla/5.0 (iPhone; CPU iPhone OS 16_0 like Mac OS X)', datetime('now', '-1 hour'));
