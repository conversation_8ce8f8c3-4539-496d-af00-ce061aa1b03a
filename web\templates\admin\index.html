<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{.title}} - 电子签名系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
            color: #333;
            line-height: 1.6;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem 2rem;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .header h1 {
            font-size: 1.5rem;
            font-weight: 600;
        }

        .container {
            max-width: 1200px;
            margin: 2rem auto;
            padding: 0 1rem;
        }

        .dashboard {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .card {
            background: white;
            border-radius: 12px;
            padding: 1.5rem;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
            border: 1px solid #e1e5e9;
            transition: all 0.3s ease;
        }

        .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }

        .stat-card {
            text-align: center;
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }

        .stat-label {
            color: #666;
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .stat-templates .stat-number { color: #667eea; }
        .stat-signatures .stat-number { color: #4CAF50; }
        .stat-today .stat-number { color: #FF9800; }
        .stat-active .stat-number { color: #9C27B0; }

        .actions {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .action-card {
            background: white;
            border-radius: 12px;
            padding: 1.5rem;
            text-align: center;
            text-decoration: none;
            color: #333;
            border: 2px solid transparent;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .action-card:hover {
            border-color: #667eea;
            transform: translateY(-2px);
            text-decoration: none;
            color: #667eea;
        }

        .action-icon {
            font-size: 2rem;
            margin-bottom: 0.5rem;
            display: block;
        }

        .action-title {
            font-weight: 600;
            margin-bottom: 0.25rem;
        }

        .action-desc {
            font-size: 0.85rem;
            color: #666;
        }

        .section {
            background: white;
            border-radius: 12px;
            padding: 1.5rem;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
            margin-bottom: 2rem;
        }

        .section-title {
            font-size: 1.25rem;
            font-weight: 600;
            margin-bottom: 1rem;
            color: #333;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .btn {
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 0.9rem;
            font-weight: 500;
            text-decoration: none;
            display: inline-block;
            transition: all 0.2s ease;
        }

        .btn-primary {
            background: #667eea;
            color: white;
        }

        .btn-primary:hover {
            background: #5a67d8;
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-secondary:hover {
            background: #5a6268;
        }

        .btn-sm {
            padding: 0.25rem 0.5rem;
            font-size: 0.75rem;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
        }

        .table th,
        .table td {
            padding: 0.75rem;
            text-align: left;
            border-bottom: 1px solid #e1e5e9;
        }

        .table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #495057;
        }

        .loading {
            text-align: center;
            padding: 2rem;
            color: #666;
        }

        .loading-spinner {
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin: 0 auto 1rem;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .empty-state {
            text-align: center;
            padding: 3rem 1rem;
            color: #666;
        }

        .empty-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
            opacity: 0.5;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>📋 电子签名系统 - 管理后台</h1>
    </div>

    <div class="container">
        <!-- 统计仪表板 -->
        <div class="dashboard">
            <div class="card stat-card stat-templates">
                <div class="stat-number" id="totalTemplates">-</div>
                <div class="stat-label">模板总数</div>
            </div>
            <div class="card stat-card stat-signatures">
                <div class="stat-number" id="totalSignatures">-</div>
                <div class="stat-label">签名总数</div>
            </div>
            <div class="card stat-card stat-today">
                <div class="stat-number" id="todaySignatures">-</div>
                <div class="stat-label">今日签名</div>
            </div>
            <div class="card stat-card stat-active">
                <div class="stat-number" id="activeTemplates">-</div>
                <div class="stat-label">活跃模板</div>
            </div>
        </div>

        <!-- 快捷操作 -->
        <div class="actions">
            <div class="action-card" onclick="showUploadModal()">
                <span class="action-icon">📄</span>
                <div class="action-title">上传模板</div>
                <div class="action-desc">创建新的PDF签署模板</div>
            </div>
            <div class="action-card" onclick="refreshData()">
                <span class="action-icon">🔄</span>
                <div class="action-title">刷新数据</div>
                <div class="action-desc">重新加载最新数据</div>
            </div>
            <div class="action-card" onclick="exportData()">
                <span class="action-icon">📊</span>
                <div class="action-title">导出数据</div>
                <div class="action-desc">导出签名记录和统计</div>
            </div>
            <div class="action-card" onclick="viewSettings()">
                <span class="action-icon">⚙️</span>
                <div class="action-title">系统设置</div>
                <div class="action-desc">配置系统参数</div>
            </div>
        </div>

        <!-- 模板管理 -->
        <div class="section">
            <div class="section-title">
                <span>📄 模板管理</span>
                <button class="btn btn-primary" onclick="showUploadModal()">上传新模板</button>
            </div>
            <div id="templateList">
                <div class="loading">
                    <div class="loading-spinner"></div>
                    <p>正在加载模板...</p>
                </div>
            </div>
        </div>

        <!-- 最近签名记录 -->
        <div class="section">
            <div class="section-title">
                <span>✍️ 最近签名记录</span>
                <button class="btn btn-secondary" onclick="loadSignatures()">查看全部</button>
            </div>
            <div id="signatureList">
                <div class="loading">
                    <div class="loading-spinner"></div>
                    <p>正在加载签名记录...</p>
                </div>
            </div>
        </div>
    </div>

    <!-- 上传模板模态框 -->
    <div id="uploadModal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3>📄 上传PDF模板</h3>
                <span class="close" onclick="closeUploadModal()">&times;</span>
            </div>
            <form id="uploadForm">
                <div class="form-group">
                    <label for="templateName">模板名称:</label>
                    <input type="text" id="templateName" name="name" required placeholder="请输入模板名称">
                </div>
                <div class="form-group">
                    <label for="pdfFile">PDF文件:</label>
                    <input type="file" id="pdfFile" name="file" accept=".pdf" required>
                    <small>支持PDF格式，最大10MB</small>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="signatureX">签名X坐标:</label>
                        <input type="number" id="signatureX" name="signature_x" step="0.1" value="400" required>
                    </div>
                    <div class="form-group">
                        <label for="signatureY">签名Y坐标:</label>
                        <input type="number" id="signatureY" name="signature_y" step="0.1" value="400" required>
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="signatureWidth">签名宽度:</label>
                        <input type="number" id="signatureWidth" name="signature_width" step="0.1" value="200">
                    </div>
                    <div class="form-group">
                        <label for="signatureHeight">签名高度:</label>
                        <input type="number" id="signatureHeight" name="signature_height" step="0.1" value="100">
                    </div>
                </div>
                <div class="form-actions">
                    <button type="submit" class="btn btn-primary">
                        <span id="uploadBtnText">上传模板</span>
                        <div id="uploadSpinner" class="loading-spinner" style="display: none; width: 16px; height: 16px; margin-left: 8px;"></div>
                    </button>
                    <button type="button" class="btn btn-secondary" onclick="closeUploadModal()">取消</button>
                </div>
            </form>
        </div>
    </div>

    <!-- 二维码显示模态框 -->
    <div id="qrCodeModal" class="modal" style="display: none;">
        <div class="modal-content qr-modal-content">
            <div class="modal-header">
                <h3>📱 模板二维码</h3>
                <span class="close" onclick="closeQRCodeModal()">&times;</span>
            </div>
            <div class="qr-modal-body">
                <div class="qr-info">
                    <h4 id="qrTemplateName">模板名称</h4>
                    <p id="qrTemplateDesc">扫描二维码进行签署</p>
                </div>
                <div class="qr-code-container">
                    <img id="qrCodeImage" src="" alt="二维码" />
                </div>
                <div class="qr-actions">
                    <button class="btn btn-primary" onclick="downloadQRCode()">下载二维码</button>
                    <button class="btn btn-secondary" onclick="copyQRLink()">复制链接</button>
                </div>
                <div class="qr-url">
                    <label>签署链接:</label>
                    <input type="text" id="qrSignUrl" readonly onclick="this.select()">
                </div>
            </div>
        </div>
    </div>

    <style>
        /* 模态框样式 */
        .modal {
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(4px);
        }

        .modal-content {
            background-color: white;
            margin: 5% auto;
            padding: 0;
            border-radius: 12px;
            width: 90%;
            max-width: 500px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
            animation: modalSlideIn 0.3s ease;
        }

        @keyframes modalSlideIn {
            from {
                opacity: 0;
                transform: translateY(-50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .modal-header {
            padding: 1.5rem;
            border-bottom: 1px solid #e1e5e9;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-header h3 {
            margin: 0;
            color: #333;
        }

        .close {
            font-size: 1.5rem;
            font-weight: bold;
            cursor: pointer;
            color: #999;
            transition: color 0.2s ease;
        }

        .close:hover {
            color: #333;
        }

        .form-group {
            margin-bottom: 1rem;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
            color: #333;
        }

        .form-group input {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 0.9rem;
            transition: border-color 0.2s ease;
        }

        .form-group input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .form-group small {
            display: block;
            margin-top: 0.25rem;
            color: #666;
            font-size: 0.8rem;
        }

        .form-actions {
            padding: 1.5rem;
            border-top: 1px solid #e1e5e9;
            display: flex;
            gap: 1rem;
            justify-content: flex-end;
        }

        #uploadForm {
            padding: 1.5rem;
        }

        /* 二维码模态框样式 */
        .qr-modal-content {
            max-width: 600px;
        }

        .qr-modal-body {
            padding: 1.5rem;
            text-align: center;
        }

        .qr-info {
            margin-bottom: 1.5rem;
        }

        .qr-info h4 {
            margin: 0 0 0.5rem 0;
            color: #333;
            font-size: 1.2rem;
        }

        .qr-info p {
            margin: 0;
            color: #666;
            font-size: 0.9rem;
        }

        .qr-code-container {
            background: white;
            border: 2px solid #e1e5e9;
            border-radius: 12px;
            padding: 2rem;
            margin: 1.5rem 0;
            display: inline-block;
        }

        .qr-code-container img {
            max-width: 200px;
            max-height: 200px;
            display: block;
        }

        .qr-actions {
            margin: 1.5rem 0;
            display: flex;
            gap: 1rem;
            justify-content: center;
        }

        .qr-url {
            margin-top: 1.5rem;
            text-align: left;
        }

        .qr-url label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
            color: #333;
        }

        .qr-url input {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 0.9rem;
            background: #f8f9fa;
            font-family: monospace;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .header {
                padding: 1rem;
            }

            .container {
                margin: 1rem auto;
                padding: 0 0.5rem;
            }

            .dashboard {
                grid-template-columns: repeat(2, 1fr);
                gap: 1rem;
            }

            .actions {
                grid-template-columns: 1fr;
            }

            .table {
                font-size: 0.85rem;
            }

            .table th,
            .table td {
                padding: 0.5rem 0.25rem;
            }

            .form-row {
                grid-template-columns: 1fr;
            }

            .modal-content {
                margin: 10% auto;
                width: 95%;
            }
        }

        @media (max-width: 480px) {
            .dashboard {
                grid-template-columns: 1fr;
            }

            .stat-number {
                font-size: 2rem;
            }
        }
    </style>

    <script>
        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadDashboardData();
            loadTemplates();
            loadRecentSignatures();
        });

        // 加载仪表板统计数据
        async function loadDashboardData() {
            try {
                const [templatesResponse, signaturesResponse] = await Promise.all([
                    fetch('/api/admin/templates'),
                    fetch('/api/admin/signatures')
                ]);

                if (templatesResponse.ok && signaturesResponse.ok) {
                    const templates = await templatesResponse.json();
                    const signatures = await signaturesResponse.json();

                    updateStats(templates, signatures);
                } else {
                    console.error('Failed to load dashboard data');
                }
            } catch (error) {
                console.error('Error loading dashboard data:', error);
            }
        }

        // 更新统计数据
        function updateStats(templates, signatures) {
            const totalTemplates = templates.length || 0;
            const totalSignatures = signatures.length || 0;

            // 计算今日签名数
            const today = new Date().toDateString();
            const todaySignatures = signatures.filter(sig =>
                new Date(sig.signed_at).toDateString() === today
            ).length;

            // 计算活跃模板数（有签名记录的模板）
            const activeTemplateIds = new Set(signatures.map(sig => sig.template_id));
            const activeTemplates = activeTemplateIds.size;

            document.getElementById('totalTemplates').textContent = totalTemplates;
            document.getElementById('totalSignatures').textContent = totalSignatures;
            document.getElementById('todaySignatures').textContent = todaySignatures;
            document.getElementById('activeTemplates').textContent = activeTemplates;
        }

        // 加载模板列表
        async function loadTemplates() {
            try {
                const response = await fetch('/api/admin/templates');
                if (response.ok) {
                    const templates = await response.json();
                    renderTemplates(templates);
                } else {
                    showTemplatesError();
                }
            } catch (error) {
                console.error('Error loading templates:', error);
                showTemplatesError();
            }
        }

        // 渲染模板列表
        function renderTemplates(templates) {
            const container = document.getElementById('templateList');

            if (templates.length === 0) {
                container.innerHTML = `
                    <div class="empty-state">
                        <div class="empty-icon">📄</div>
                        <p>暂无模板，点击上方按钮上传第一个模板</p>
                    </div>
                `;
                return;
            }

            const tableHTML = `
                <table class="table">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>模板名称</th>
                            <th>创建时间</th>
                            <th>签名位置</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${templates.map(template => `
                            <tr>
                                <td>#${template.id}</td>
                                <td>${template.name}</td>
                                <td>${formatDateTime(template.created_at)}</td>
                                <td>(${template.signature_x}, ${template.signature_y})</td>
                                <td>
                                    <button class="btn btn-primary btn-sm" onclick="generateQRCode(${template.id})">生成二维码</button>
                                    <button class="btn btn-secondary btn-sm" onclick="viewQRCode(${template.id})">查看二维码</button>
                                    <button class="btn btn-secondary btn-sm" onclick="previewTemplate(${template.id})">预览模板</button>
                                </td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            `;

            container.innerHTML = tableHTML;
        }

        // 显示模板加载错误
        function showTemplatesError() {
            const container = document.getElementById('templateList');
            container.innerHTML = `
                <div class="empty-state">
                    <div class="empty-icon">❌</div>
                    <p>加载模板失败</p>
                    <button class="btn btn-primary" onclick="loadTemplates()">重试</button>
                </div>
            `;
        }

        // 加载最近签名记录
        async function loadRecentSignatures() {
            try {
                const response = await fetch('/api/admin/signatures');
                if (response.ok) {
                    const signatures = await response.json();
                    renderRecentSignatures(signatures.slice(0, 10)); // 只显示最近10条
                } else {
                    showSignaturesError();
                }
            } catch (error) {
                console.error('Error loading signatures:', error);
                showSignaturesError();
            }
        }

        // 渲染最近签名记录
        function renderRecentSignatures(signatures) {
            const container = document.getElementById('signatureList');

            if (signatures.length === 0) {
                container.innerHTML = `
                    <div class="empty-state">
                        <div class="empty-icon">✍️</div>
                        <p>暂无签名记录</p>
                    </div>
                `;
                return;
            }

            const tableHTML = `
                <table class="table">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>签名人</th>
                            <th>模板</th>
                            <th>签名时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${signatures.map(signature => `
                            <tr>
                                <td>#${signature.id}</td>
                                <td>${signature.signer_name}</td>
                                <td>${signature.template ? signature.template.name : '未知模板'}</td>
                                <td>${formatDateTime(signature.signed_at)}</td>
                                <td>
                                    <button class="btn btn-primary btn-sm" onclick="previewSignature(${signature.id})">预览</button>
                                    <button class="btn btn-secondary btn-sm" onclick="downloadSignature(${signature.id})">下载</button>
                                </td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            `;

            container.innerHTML = tableHTML;
        }

        // 显示签名记录加载错误
        function showSignaturesError() {
            const container = document.getElementById('signatureList');
            container.innerHTML = `
                <div class="empty-state">
                    <div class="empty-icon">❌</div>
                    <p>加载签名记录失败</p>
                    <button class="btn btn-primary" onclick="loadRecentSignatures()">重试</button>
                </div>
            `;
        }

        // 显示上传模态框
        function showUploadModal() {
            document.getElementById('uploadModal').style.display = 'block';
            document.body.style.overflow = 'hidden';
        }

        // 关闭上传模态框
        function closeUploadModal() {
            document.getElementById('uploadModal').style.display = 'none';
            document.body.style.overflow = 'auto';
            document.getElementById('uploadForm').reset();
        }

        // 处理模板上传
        document.getElementById('uploadForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            const submitBtn = e.target.querySelector('button[type="submit"]');
            const btnText = document.getElementById('uploadBtnText');
            const spinner = document.getElementById('uploadSpinner');

            // 显示加载状态
            submitBtn.disabled = true;
            btnText.textContent = '上传中...';
            spinner.style.display = 'inline-block';

            try {
                const formData = new FormData(e.target);
                const response = await fetch('/api/admin/templates', {
                    method: 'POST',
                    body: formData
                });

                if (response.ok) {
                    const result = await response.json();
                    alert('模板上传成功！');
                    closeUploadModal();
                    loadTemplates();
                    loadDashboardData();
                } else {
                    const error = await response.json();
                    alert('上传失败: ' + (error.error || '未知错误'));
                }
            } catch (error) {
                console.error('Upload error:', error);
                alert('上传失败: 网络错误');
            } finally {
                // 恢复按钮状态
                submitBtn.disabled = false;
                btnText.textContent = '上传模板';
                spinner.style.display = 'none';
            }
        });

        // 生成二维码
        async function generateQRCode(templateId) {
            try {
                const response = await fetch(`/api/admin/templates/${templateId}/qrcode`, {
                    method: 'POST'
                });

                if (response.ok) {
                    const result = await response.json();
                    alert('二维码生成成功！');
                    // 重新加载模板列表以更新状态
                    loadTemplates();
                } else {
                    const error = await response.json();
                    alert('生成二维码失败: ' + (error.error || '未知错误'));
                }
            } catch (error) {
                console.error('QR code generation error:', error);
                alert('生成二维码失败: 网络错误');
            }
        }

        // 查看二维码
        async function viewQRCode(templateId) {
            try {
                // 首先检查模板信息，获取二维码路径
                const response = await fetch(`/api/admin/templates/${templateId}`);

                if (response.ok) {
                    const template = await response.json();
                    if (template.qr_code_path) {
                        // 显示二维码模态框
                        showQRCodeModal(template);
                    } else {
                        // 如果没有二维码，提示生成
                        if (confirm('该模板还没有二维码，是否现在生成？')) {
                            generateQRCode(templateId);
                        }
                    }
                } else {
                    alert('获取模板信息失败');
                }
            } catch (error) {
                console.error('Error viewing QR code:', error);
                alert('查看二维码失败: 网络错误');
            }
        }

        // 显示二维码模态框
        function showQRCodeModal(template) {
            // 设置模板信息
            document.getElementById('qrTemplateName').textContent = template.name;
            document.getElementById('qrTemplateDesc').textContent = `模板ID: ${template.id} | 创建时间: ${formatDateTime(template.created_at)}`;

            // 设置二维码图片
            const qrCodeImage = document.getElementById('qrCodeImage');
            qrCodeImage.src = `/${template.qr_code_path}`;
            qrCodeImage.onerror = function() {
                this.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZjhmOWZhIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzk5OSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPuS6jOe7tOeggeS4jeWtmOWcqDwvdGV4dD48L3N2Zz4=';
            };

            // 设置签署链接
            const signUrl = `${window.location.origin}/sign/${template.id}`;
            document.getElementById('qrSignUrl').value = signUrl;

            // 存储当前模板信息供其他函数使用
            window.currentTemplate = template;

            // 显示模态框
            document.getElementById('qrCodeModal').style.display = 'block';
            document.body.style.overflow = 'hidden';
        }

        // 关闭二维码模态框
        function closeQRCodeModal() {
            document.getElementById('qrCodeModal').style.display = 'none';
            document.body.style.overflow = 'auto';
            window.currentTemplate = null;
        }

        // 下载二维码
        function downloadQRCode() {
            if (!window.currentTemplate) return;

            const qrCodeImage = document.getElementById('qrCodeImage');
            const link = document.createElement('a');
            link.href = qrCodeImage.src;
            link.download = `qrcode_template_${window.currentTemplate.id}_${window.currentTemplate.name}.png`;
            link.style.display = 'none';

            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }

        // 复制签署链接
        async function copyQRLink() {
            const urlInput = document.getElementById('qrSignUrl');

            try {
                await navigator.clipboard.writeText(urlInput.value);

                // 临时改变按钮文本以显示成功状态
                const btn = event.target;
                const originalText = btn.textContent;
                btn.textContent = '已复制!';
                btn.style.background = '#4CAF50';

                setTimeout(() => {
                    btn.textContent = originalText;
                    btn.style.background = '';
                }, 2000);

            } catch (err) {
                // 如果现代API不可用，使用传统方法
                urlInput.select();
                urlInput.setSelectionRange(0, 99999);
                document.execCommand('copy');

                alert('链接已复制到剪贴板');
            }
        }

        // 预览模板
        function previewTemplate(templateId) {
            window.open(`/api/admin/templates/${templateId}/preview`, '_blank');
        }

        // 预览签名文档
        function previewSignature(signatureId) {
            window.open(`/api/preview/${signatureId}`, '_blank');
        }

        // 下载签名文档
        function downloadSignature(signatureId) {
            window.open(`/api/download/${signatureId}`, '_blank');
        }

        // 刷新所有数据
        function refreshData() {
            loadDashboardData();
            loadTemplates();
            loadRecentSignatures();
        }

        // 导出数据（占位函数）
        function exportData() {
            alert('导出功能开发中...');
        }

        // 查看设置（占位函数）
        function viewSettings() {
            alert('设置功能开发中...');
        }

        // 加载所有签名记录
        function loadSignatures() {
            alert('完整签名记录页面开发中...');
        }

        // 格式化日期时间
        function formatDateTime(dateString) {
            if (!dateString) return '-';

            try {
                const date = new Date(dateString);
                return date.toLocaleString('zh-CN', {
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit',
                    hour: '2-digit',
                    minute: '2-digit'
                });
            } catch (error) {
                return dateString;
            }
        }

        // 自动刷新数据（每30秒）
        setInterval(() => {
            loadDashboardData();
        }, 30000);

        // 键盘快捷键
        document.addEventListener('keydown', function(e) {
            // Ctrl/Cmd + R 刷新数据
            if ((e.ctrlKey || e.metaKey) && e.key === 'r') {
                e.preventDefault();
                refreshData();
            }

            // Ctrl/Cmd + U 上传模板
            if ((e.ctrlKey || e.metaKey) && e.key === 'u') {
                e.preventDefault();
                showUploadModal();
            }

            // ESC 关闭模态框
            if (e.key === 'Escape') {
                closeUploadModal();
                closeQRCodeModal();
            }
        });

        // 点击模态框外部关闭
        document.getElementById('uploadModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeUploadModal();
            }
        });

        document.getElementById('qrCodeModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeQRCodeModal();
            }
        });
    </script>
</body>
</html>
