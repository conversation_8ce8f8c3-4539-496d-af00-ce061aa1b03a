package handler

import (
	"fmt"
	"io"
	"mime/multipart"
	"net/http"
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	"esign/internal/model"
	"esign/internal/service"

	"github.com/gin-gonic/gin"
)

// AdminHandler 后台管理处理器
type AdminHandler struct {
	templateService  service.TemplateService
	signatureService service.SignatureService
}

// NewAdminHandler 创建后台管理处理器实例
func NewAdminHandler(templateService service.TemplateService, signatureService service.SignatureService) *AdminHandler {
	return &AdminHandler{
		templateService:  templateService,
		signatureService: signatureService,
	}
}

// CreateTemplate 创建模板
func (h *AdminHandler) CreateTemplate(c *gin.Context) {
	// 解析multipart表单
	if err := c.Request.ParseMultipartForm(32 << 20); err != nil { // 32MB max
		c.<PERSON>(http.StatusBadRequest, gin.H{"error": "Failed to parse form: " + err.Error()})
		return
	}

	// 获取表单字段
	name := c.PostForm("name")
	signatureX := c.PostForm("signature_x")
	signatureY := c.PostForm("signature_y")
	signatureWidth := c.PostForm("signature_width")
	signatureHeight := c.PostForm("signature_height")

	if name == "" || signatureX == "" || signatureY == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Missing required fields"})
		return
	}

	// 转换坐标参数
	x, err := strconv.ParseFloat(signatureX, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid signature_x"})
		return
	}

	y, err := strconv.ParseFloat(signatureY, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid signature_y"})
		return
	}

	width := 100.0
	if signatureWidth != "" {
		if w, err := strconv.ParseFloat(signatureWidth, 64); err == nil {
			width = w
		}
	}

	height := 50.0
	if signatureHeight != "" {
		if h, err := strconv.ParseFloat(signatureHeight, 64); err == nil {
			height = h
		}
	}

	// 处理文件上传
	file, header, err := c.Request.FormFile("file")
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "No file uploaded: " + err.Error()})
		return
	}
	defer file.Close()

	// 验证文件类型
	if !strings.HasSuffix(strings.ToLower(header.Filename), ".pdf") {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Only PDF files are allowed"})
		return
	}

	// 保存上传的文件
	filePath, err := h.saveUploadedFile(file, header)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to save file: " + err.Error()})
		return
	}

	// 创建模板请求
	req := &model.CreateTemplateRequest{
		Name:            name,
		SignatureX:      x,
		SignatureY:      y,
		SignatureWidth:  width,
		SignatureHeight: height,
	}

	template, err := h.templateService.CreateTemplate(req, filePath)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusCreated, template)
}

// GetTemplates 获取模板列表
func (h *AdminHandler) GetTemplates(c *gin.Context) {
	templates, err := h.templateService.GetAllTemplates()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, templates)
}

// GetTemplate 获取单个模板
func (h *AdminHandler) GetTemplate(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid template ID"})
		return
	}

	template, err := h.templateService.GetTemplate(uint(id))
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Template not found"})
		return
	}

	c.JSON(http.StatusOK, template)
}

// UpdateTemplate 更新模板
func (h *AdminHandler) UpdateTemplate(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid template ID"})
		return
	}

	var req model.UpdateTemplateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	template, err := h.templateService.UpdateTemplate(uint(id), &req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, template)
}

// DeleteTemplate 删除模板
func (h *AdminHandler) DeleteTemplate(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid template ID"})
		return
	}

	err = h.templateService.DeleteTemplate(uint(id))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Template deleted successfully"})
}

// GetSignatures 获取签署记录
func (h *AdminHandler) GetSignatures(c *gin.Context) {
	signatures, err := h.signatureService.GetAllSignatures()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, signatures)
}

// saveUploadedFile 保存上传的文件
func (h *AdminHandler) saveUploadedFile(file multipart.File, header *multipart.FileHeader) (string, error) {
	// 生成唯一文件名
	timestamp := time.Now().Format("20060102150405")
	filename := fmt.Sprintf("%s_%s", timestamp, header.Filename)
	filePath := filepath.Join("storage", "templates", filename)

	// 确保目录存在
	dir := filepath.Dir(filePath)
	if err := os.MkdirAll(dir, 0755); err != nil {
		return "", err
	}

	// 创建目标文件
	dst, err := os.Create(filePath)
	if err != nil {
		return "", err
	}
	defer dst.Close()

	// 复制文件内容
	if _, err := io.Copy(dst, file); err != nil {
		return "", err
	}

	return filePath, nil
}

// GenerateQRCode 生成模板二维码
func (h *AdminHandler) GenerateQRCode(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid template ID"})
		return
	}

	// 获取基础URL（从配置或请求头获取）
	baseURL := c.GetHeader("X-Base-URL")
	if baseURL == "" {
		baseURL = fmt.Sprintf("%s://%s", getScheme(c), c.Request.Host)
	}

	// 生成二维码
	qrCodePath, err := h.templateService.GenerateQRCode(uint(id), baseURL)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"qr_code_path": qrCodePath,
		"message":      "QR code generated successfully",
	})
}

// getScheme 获取请求协议
func getScheme(c *gin.Context) string {
	if c.Request.TLS != nil {
		return "https"
	}
	if scheme := c.GetHeader("X-Forwarded-Proto"); scheme != "" {
		return scheme
	}
	return "http"
}
