package middleware

import (
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
)

// Metrics 性能指标结构
type Metrics struct {
	RequestCount    int64
	TotalDuration   time.Duration
	AverageDuration time.Duration
	ErrorCount      int64
	StatusCodes     map[int]int64
}

var globalMetrics = &Metrics{
	StatusCodes: make(map[int]int64),
}

// MetricsMiddleware 性能监控中间件
func MetricsMiddleware() gin.HandlerFunc {
	return gin.HandlerFunc(func(c *gin.Context) {
		start := time.Now()
		
		c.Next()
		
		// 计算请求耗时
		duration := time.Since(start)
		
		// 更新指标
		globalMetrics.RequestCount++
		globalMetrics.TotalDuration += duration
		globalMetrics.AverageDuration = time.Duration(int64(globalMetrics.TotalDuration) / globalMetrics.RequestCount)
		
		// 统计状态码
		statusCode := c.Writer.Status()
		globalMetrics.StatusCodes[statusCode]++
		
		// 统计错误
		if statusCode >= 400 {
			globalMetrics.ErrorCount++
		}
		
		// 记录慢请求
		if duration > time.Second {
			c.Header("X-Slow-Request", "true")
		}
	})
}

// GetMetrics 获取性能指标
func GetMetrics() *Metrics {
	return globalMetrics
}

// MetricsHandler 性能指标处理器
func MetricsHandler() gin.HandlerFunc {
	return gin.HandlerFunc(func(c *gin.Context) {
		metrics := GetMetrics()
		
		c.JSON(200, gin.H{
			"request_count":    metrics.RequestCount,
			"total_duration":   metrics.TotalDuration.String(),
			"average_duration": metrics.AverageDuration.String(),
			"error_count":      metrics.ErrorCount,
			"error_rate":       float64(metrics.ErrorCount) / float64(metrics.RequestCount) * 100,
			"status_codes":     metrics.StatusCodes,
		})
	})
}

// HealthCheck 健康检查处理器
func HealthCheck() gin.HandlerFunc {
	return gin.HandlerFunc(func(c *gin.Context) {
		c.JSON(200, gin.H{
			"status":    "healthy",
			"timestamp": time.Now().Unix(),
			"uptime":    time.Since(startTime).String(),
		})
	})
}

var startTime = time.Now()

// RequestSizeLimit 请求大小限制中间件
func RequestSizeLimit(maxSize int64) gin.HandlerFunc {
	return gin.HandlerFunc(func(c *gin.Context) {
		if c.Request.ContentLength > maxSize {
			c.JSON(413, gin.H{
				"error": "Request entity too large",
			})
			c.Abort()
			return
		}
		c.Next()
	})
}

// ResponseTime 响应时间头中间件
func ResponseTime() gin.HandlerFunc {
	return gin.HandlerFunc(func(c *gin.Context) {
		start := time.Now()
		c.Next()
		duration := time.Since(start)
		c.Header("X-Response-Time", duration.String())
	})
}

// CacheControl 缓存控制中间件
func CacheControl(maxAge int) gin.HandlerFunc {
	return gin.HandlerFunc(func(c *gin.Context) {
		// 对静态资源设置缓存
		if isStaticResource(c.Request.URL.Path) {
			c.Header("Cache-Control", "public, max-age="+strconv.Itoa(maxAge))
		} else {
			// API响应不缓存
			c.Header("Cache-Control", "no-cache, no-store, must-revalidate")
			c.Header("Pragma", "no-cache")
			c.Header("Expires", "0")
		}
		c.Next()
	})
}

func isStaticResource(path string) bool {
	staticPaths := []string{"/static/", "/storage/"}
	for _, staticPath := range staticPaths {
		if len(path) >= len(staticPath) && path[:len(staticPath)] == staticPath {
			return true
		}
	}
	return false
}
