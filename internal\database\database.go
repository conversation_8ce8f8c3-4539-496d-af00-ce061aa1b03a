package database

import (
	"database/sql"
	"fmt"
	"os"
	"path/filepath"

	"esign/internal/repository"

	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
	_ "modernc.org/sqlite"
)

// Database 数据库管理结构
type Database struct {
	DB *gorm.DB
}

// Config 数据库配置
type Config struct {
	DatabasePath string
	LogLevel     logger.LogLevel
}

// NewDatabase 创建数据库实例
func NewDatabase(config *Config) (*Database, error) {
	// 确保数据库目录存在
	dbDir := filepath.Dir(config.DatabasePath)
	if err := os.MkdirAll(dbDir, 0755); err != nil {
		return nil, fmt.Errorf("failed to create database directory: %w", err)
	}

	// 配置GORM
	gormConfig := &gorm.Config{
		Logger: logger.Default.LogMode(config.LogLevel),
	}

	// 首先使用modernc.org/sqlite创建连接，添加UTF-8编码参数
	dsn := config.DatabasePath + "?_pragma=foreign_keys(1)&_pragma=journal_mode(WAL)"
	sqlDB, err := sql.Open("sqlite", dsn)
	if err != nil {
		return nil, fmt.Errorf("failed to open database: %w", err)
	}

	// 使用现有连接创建GORM实例
	db, err := gorm.Open(sqlite.Dialector{Conn: sqlDB}, gormConfig)
	if err != nil {
		sqlDB.Close()
		return nil, fmt.Errorf("failed to connect to database: %w", err)
	}

	// 设置连接池参数
	sqlDB.SetMaxOpenConns(1) // SQLite只支持单个写连接
	sqlDB.SetMaxIdleConns(1)

	// 外键约束、WAL模式和UTF-8编码已在DSN中设置
	// 额外确保UTF-8编码
	if _, err := sqlDB.Exec("PRAGMA encoding = 'UTF-8'"); err != nil {
		return nil, fmt.Errorf("failed to set UTF-8 encoding: %w", err)
	}

	return &Database{DB: db}, nil
}

// AutoMigrate 自动迁移数据库表
func (d *Database) AutoMigrate() error {
	return d.DB.AutoMigrate(
		&repository.Template{},
		&repository.Signature{},
	)
}

// Close 关闭数据库连接
func (d *Database) Close() error {
	sqlDB, err := d.DB.DB()
	if err != nil {
		return err
	}
	return sqlDB.Close()
}

// GetDB 获取数据库实例
func (d *Database) GetDB() *gorm.DB {
	return d.DB
}

// HealthCheck 数据库健康检查
func (d *Database) HealthCheck() error {
	sqlDB, err := d.DB.DB()
	if err != nil {
		return err
	}
	return sqlDB.Ping()
}

// GetStats 获取数据库统计信息
func (d *Database) GetStats() (map[string]interface{}, error) {
	stats := make(map[string]interface{})

	// 获取模板数量
	var templateCount int64
	if err := d.DB.Model(&repository.Template{}).Count(&templateCount).Error; err != nil {
		return nil, err
	}
	stats["template_count"] = templateCount

	// 获取签署记录数量
	var signatureCount int64
	if err := d.DB.Model(&repository.Signature{}).Count(&signatureCount).Error; err != nil {
		return nil, err
	}
	stats["signature_count"] = signatureCount

	// 获取数据库文件大小
	sqlDB, err := d.DB.DB()
	if err != nil {
		return nil, err
	}

	var dbSize int64
	row := sqlDB.QueryRow("SELECT page_count * page_size as size FROM pragma_page_count(), pragma_page_size()")
	if err := row.Scan(&dbSize); err != nil {
		return nil, err
	}
	stats["database_size_bytes"] = dbSize

	return stats, nil
}

// Backup 备份数据库
func (d *Database) Backup(backupPath string) error {
	// 确保备份目录存在
	backupDir := filepath.Dir(backupPath)
	if err := os.MkdirAll(backupDir, 0755); err != nil {
		return fmt.Errorf("failed to create backup directory: %w", err)
	}

	// 执行备份
	sql := fmt.Sprintf("VACUUM INTO '%s'", backupPath)
	return d.DB.Exec(sql).Error
}

// Transaction 执行事务
func (d *Database) Transaction(fn func(*gorm.DB) error) error {
	return d.DB.Transaction(fn)
}
