package handler

import (
	"bytes"
	"encoding/json"
	"errors"
	"net/http"
	"net/http/httptest"
	"testing"

	"esign/internal/model"
	"esign/internal/service"

	"github.com/gin-gonic/gin"
)

// 测试用错误定义
var (
	ErrTemplateNotFound  = errors.New("template not found")
	ErrSignatureNotFound = errors.New("signature not found")
)

// MockTemplateService 模拟模板服务
type MockTemplateService struct {
	templates map[uint]*model.Template
}

func NewMockTemplateService() *MockTemplateService {
	return &MockTemplateService{
		templates: make(map[uint]*model.Template),
	}
}

func (m *MockTemplateService) CreateTemplate(req *model.CreateTemplateRequest, filePath string) (*model.Template, error) {
	template := &model.Template{
		ID:              uint(len(m.templates) + 1),
		Name:            req.Name,
		FilePath:        filePath,
		SignatureX:      req.SignatureX,
		SignatureY:      req.SignatureY,
		SignatureWidth:  req.SignatureWidth,
		SignatureHeight: req.SignatureHeight,
	}
	m.templates[template.ID] = template
	return template, nil
}

func (m *MockTemplateService) GetTemplate(id uint) (*model.Template, error) {
	if template, exists := m.templates[id]; exists {
		return template, nil
	}
	return nil, ErrTemplateNotFound
}

func (m *MockTemplateService) GetAllTemplates() ([]*model.Template, error) {
	var templates []*model.Template
	for _, template := range m.templates {
		templates = append(templates, template)
	}
	return templates, nil
}

func (m *MockTemplateService) UpdateTemplate(id uint, req *model.UpdateTemplateRequest) (*model.Template, error) {
	if template, exists := m.templates[id]; exists {
		if req.Name != "" {
			template.Name = req.Name
		}
		return template, nil
	}
	return nil, ErrTemplateNotFound
}

func (m *MockTemplateService) DeleteTemplate(id uint) error {
	if _, exists := m.templates[id]; exists {
		delete(m.templates, id)
		return nil
	}
	return ErrTemplateNotFound
}

func (m *MockTemplateService) GenerateQRCode(templateID uint, baseURL string) (string, error) {
	return "qrcode_path.png", nil
}

// MockSignatureService 模拟签署服务
type MockSignatureService struct {
	signatures map[uint]*model.SignatureResponse
}

func NewMockSignatureService() *MockSignatureService {
	return &MockSignatureService{
		signatures: make(map[uint]*model.SignatureResponse),
	}
}

func (m *MockSignatureService) CreateSignature(templateID uint, req *model.CreateSignatureRequest, ipAddress, userAgent string) (*model.SignatureResponse, error) {
	signature := &model.SignatureResponse{
		ID:         uint(len(m.signatures) + 1),
		TemplateID: templateID,
		SignerName: req.SignerName,
	}
	m.signatures[signature.ID] = signature
	return signature, nil
}

func (m *MockSignatureService) GetSignature(id uint) (*model.Signature, error) {
	return nil, ErrSignatureNotFound
}

func (m *MockSignatureService) GetSignaturesByTemplate(templateID uint) ([]*model.Signature, error) {
	return nil, nil
}

func (m *MockSignatureService) GetAllSignatures() ([]*model.Signature, error) {
	return nil, nil
}

func (m *MockSignatureService) ProcessSignedDocument(signatureID uint) error {
	return nil
}

// MockPDFService 模拟PDF服务
type MockPDFService struct{}

func NewMockPDFService() *MockPDFService {
	return &MockPDFService{}
}

func (m *MockPDFService) ProcessSignedDocument(templatePath, signaturePath, outputPath string, x, y, width, height float64) error {
	return nil
}

func (m *MockPDFService) ValidatePDF(filePath string) error {
	return nil
}

func (m *MockPDFService) GetPDFInfo(filePath string) (*service.PDFInfo, error) {
	return &service.PDFInfo{
		PageCount: 1,
		Width:     595,
		Height:    842,
		Title:     "Test PDF",
	}, nil
}

func (m *MockPDFService) CreateSignedPDF(templatePath, signaturePath, outputPath string, signatureConfig *service.SignatureConfig) error {
	return nil
}

// TestSignHandler_GetSignPage 测试获取签署页面
func TestSignHandler_GetSignPage(t *testing.T) {
	// 设置Gin为测试模式
	gin.SetMode(gin.TestMode)

	// 创建模拟服务
	templateService := NewMockTemplateService()
	signatureService := NewMockSignatureService()
	pdfService := NewMockPDFService()

	// 创建测试模板
	template, _ := templateService.CreateTemplate(&model.CreateTemplateRequest{
		Name:       "Test Template",
		SignatureX: 100,
		SignatureY: 200,
	}, "test.pdf")

	// 创建处理器
	handler := NewSignHandler(templateService, signatureService, pdfService)

	// 创建路由
	router := gin.New()
	router.GET("/api/sign/:template_id", handler.GetSignPage)

	// 创建测试请求
	req, _ := http.NewRequest("GET", "/api/sign/1", nil)
	w := httptest.NewRecorder()

	// 执行请求
	router.ServeHTTP(w, req)

	// 验证响应
	if w.Code != http.StatusOK {
		t.Errorf("Expected status code %d, got %d", http.StatusOK, w.Code)
	}

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	if err != nil {
		t.Fatalf("Failed to unmarshal response: %v", err)
	}

	if response["message"] != "Ready to sign" {
		t.Errorf("Expected message 'Ready to sign', got %v", response["message"])
	}

	// 验证模板信息
	templateData, ok := response["template"].(map[string]interface{})
	if !ok {
		t.Fatal("Template data not found in response")
	}

	if templateData["name"] != template.Name {
		t.Errorf("Expected template name %s, got %v", template.Name, templateData["name"])
	}
}

// TestSignHandler_SubmitSignature 测试提交签名
func TestSignHandler_SubmitSignature(t *testing.T) {
	gin.SetMode(gin.TestMode)

	templateService := NewMockTemplateService()
	signatureService := NewMockSignatureService()
	pdfService := NewMockPDFService()

	// 创建测试模板
	templateService.CreateTemplate(&model.CreateTemplateRequest{
		Name:       "Test Template",
		SignatureX: 100,
		SignatureY: 200,
	}, "test.pdf")

	handler := NewSignHandler(templateService, signatureService, pdfService)

	router := gin.New()
	router.POST("/api/sign/:template_id", handler.SubmitSignature)

	// 创建请求数据
	requestData := model.CreateSignatureRequest{
		SignerName:    "Test User",
		SignatureData: "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==",
	}

	jsonData, _ := json.Marshal(requestData)
	req, _ := http.NewRequest("POST", "/api/sign/1", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")
	w := httptest.NewRecorder()

	router.ServeHTTP(w, req)

	if w.Code != http.StatusCreated {
		t.Errorf("Expected status code %d, got %d", http.StatusCreated, w.Code)
	}

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	if err != nil {
		t.Fatalf("Failed to unmarshal response: %v", err)
	}

	if response["message"] != "Signature submitted successfully" {
		t.Errorf("Expected message 'Signature submitted successfully', got %v", response["message"])
	}
}

// TestSignHandler_GetSignPage_NotFound 测试模板不存在的情况
func TestSignHandler_GetSignPage_NotFound(t *testing.T) {
	gin.SetMode(gin.TestMode)

	templateService := NewMockTemplateService()
	signatureService := NewMockSignatureService()
	pdfService := NewMockPDFService()

	handler := NewSignHandler(templateService, signatureService, pdfService)

	router := gin.New()
	router.GET("/api/sign/:template_id", handler.GetSignPage)

	req, _ := http.NewRequest("GET", "/api/sign/999", nil)
	w := httptest.NewRecorder()

	router.ServeHTTP(w, req)

	if w.Code != http.StatusNotFound {
		t.Errorf("Expected status code %d, got %d", http.StatusNotFound, w.Code)
	}
}
