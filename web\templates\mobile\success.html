<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <meta name="format-detection" content="telephone=no">
    <title>签名成功 - 电子签名系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .container {
            background: white;
            border-radius: 16px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            max-width: 400px;
            width: 100%;
            overflow: hidden;
            opacity: 1;
            transform: translateY(0);
        }

        .success-header {
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
            color: white;
            padding: 40px 20px;
            text-align: center;
            position: relative;
        }

        .success-icon {
            width: 80px;
            height: 80px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            transform: scale(1);
            opacity: 1;
        }

        .success-icon::before {
            content: '✓';
            font-size: 40px;
            font-weight: bold;
            color: white;
        }

        .success-title {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 8px;
        }

        .success-subtitle {
            font-size: 14px;
            opacity: 0.9;
        }

        .content {
            padding: 30px 20px;
        }

        .signature-info {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 25px;
        }

        .info-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
            padding-bottom: 12px;
            border-bottom: 1px solid #e9ecef;
        }

        .info-row:last-child {
            margin-bottom: 0;
            padding-bottom: 0;
            border-bottom: none;
        }

        .info-label {
            font-size: 14px;
            color: #666;
            font-weight: 500;
        }

        .info-value {
            font-size: 14px;
            color: #333;
            font-weight: 600;
            text-align: right;
            max-width: 60%;
            word-break: break-all;
        }

        .template-name {
            color: #667eea;
        }

        .user-name {
            color: #4CAF50;
        }

        .actions {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .btn {
            padding: 14px 20px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s;
            text-decoration: none;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .btn-primary {
            background: #667eea;
            color: white;
        }

        .btn-primary:hover {
            background: #5a67d8;
            transform: translateY(-1px);
        }

        .btn-secondary {
            background: #f8f9fa;
            color: #666;
            border: 2px solid #e9ecef;
        }

        .btn-secondary:hover {
            background: #e9ecef;
            border-color: #dee2e6;
        }

        .btn-icon {
            font-size: 18px;
        }

        .footer {
            text-align: center;
            padding: 20px;
            background: #f8f9fa;
            color: #666;
            font-size: 12px;
        }

        .footer a {
            color: #667eea;
            text-decoration: none;
        }

        .footer a:hover {
            text-decoration: underline;
        }

        /* 加载状态 */
        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }

        .loading-spinner {
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* 响应式设计 */
        @media (max-width: 480px) {
            body {
                padding: 10px;
            }
            
            .container {
                margin: 0;
            }
            
            .success-header {
                padding: 30px 15px;
            }
            
            .content {
                padding: 20px 15px;
            }
            
            .success-title {
                font-size: 20px;
            }
            
            .info-value {
                max-width: 50%;
                font-size: 13px;
            }
        }

        /* 打印样式 */
        @media print {
            body {
                background: white;
            }
            
            .container {
                box-shadow: none;
                border: 1px solid #ddd;
            }
            
            .actions,
            .footer {
                display: none;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="success-header">
            <div class="success-icon"></div>
            <h1 class="success-title">签名成功！</h1>
            <p class="success-subtitle">您的电子签名已成功提交并保存</p>
        </div>

        <div class="content">
            <div class="signature-info">
                <div class="info-row">
                    <span class="info-label">签名文档</span>
                    <span class="info-value template-name">{{.template.Name}}</span>
                </div>
                <div class="info-row">
                    <span class="info-label">签名人</span>
                    <span class="info-value user-name">{{.signature.SignerName}}</span>
                </div>
                <div class="info-row">
                    <span class="info-label">签名时间</span>
                    <span class="info-value" id="signedAt">{{.signature.SignedAt}}</span>
                </div>
                <div class="info-row">
                    <span class="info-label">签名ID</span>
                    <span class="info-value">#{{.signature.ID}}</span>
                </div>
            </div>

            <div class="actions">
                <button class="btn btn-primary" id="previewBtn" onclick="previewDocument()">
                    <span class="btn-icon">📄</span>
                    预览已签署文档
                </button>

                <button class="btn btn-secondary" id="downloadBtn" onclick="downloadDocument()">
                    <span class="btn-icon">�</span>
                    下载签署文档
                </button>
            </div>

            <!-- 加载状态 -->
            <div class="loading" id="loading">
                <div class="loading-spinner"></div>
                <p>正在加载文档...</p>
            </div>
        </div>

        <div class="footer">
            <p>感谢使用电子签名系统</p>
            <p>如有问题，请联系 <a href="mailto:<EMAIL>">技术支持</a></p>
        </div>
    </div>

    <script>
        // 传递签署信息到JavaScript
        window.signatureData = {
            id: "{{.signature.ID}}",
            signerName: "{{.signature.SignerName}}",
            signedAt: "{{.signature.SignedAt}}",
            templateName: "{{.template.Name}}"
        };

        // 格式化显示时间
        document.addEventListener('DOMContentLoaded', function() {
            const signedAtElement = document.getElementById('signedAt');
            if (signedAtElement && window.signatureData.signedAt) {
                const date = new Date(window.signatureData.signedAt);
                signedAtElement.textContent = date.toLocaleString('zh-CN');
            }
        });

        // 预览已签署文档
        function previewDocument() {
            const btn = document.getElementById('previewBtn');
            const originalText = btn.innerHTML;

            btn.disabled = true;
            btn.innerHTML = '<span class="btn-icon">📄</span>预览中...<div class="loading-spinner" style="display:inline-block;width:16px;height:16px;margin-left:8px;"></div>';

            fetch(`/api/preview/${window.signatureData.id}`)
                .then(response => response.json())
                .then(data => {
                    if (data.document_info && data.document_info.document_url) {
                        window.open(data.document_info.document_url, '_blank');
                    } else {
                        alert('预览失败：' + (data.error || '未知错误'));
                    }
                })
                .catch(error => {
                    console.error('预览错误:', error);
                    alert('预览失败：网络错误');
                })
                .finally(() => {
                    btn.disabled = false;
                    btn.innerHTML = originalText;
                });
        }

        // 下载文档
        function downloadDocument() {
            const btn = document.getElementById('downloadBtn');
            const originalText = btn.innerHTML;

            btn.disabled = true;
            btn.innerHTML = '<span class="btn-icon">📥</span>下载中...<div class="loading-spinner" style="display:inline-block;width:16px;height:16px;margin-left:8px;"></div>';

            // 创建隐藏的下载链接
            const link = document.createElement('a');
            link.href = `/api/download/${window.signatureData.id}`;
            link.download = `signed_${window.signatureData.templateName}_${window.signatureData.signerName}.pdf`;
            link.style.display = 'none';

            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            // 恢复按钮状态
            setTimeout(() => {
                btn.disabled = false;
                btn.innerHTML = originalText;
            }, 2000);
        }

        // 分享签名记录
        function shareSignature() {
            const signatureInfo = {
                templateName: '{{.template.Name}}',
                userName: '{{.signature.SignerName}}',
                signedAt: '{{.signature.SignedAt}}',
                signatureId: '{{.signature.ID}}'
            };
            
            const shareText = `我已成功完成电子签名：\n` +
                            `文档：${signatureInfo.templateName}\n` +
                            `签名人：${signatureInfo.userName}\n` +
                            `时间：${signatureInfo.signedAt}\n` +
                            `签名ID：#${signatureInfo.signatureId}`;
            
            // 检查是否支持Web Share API
            if (navigator.share) {
                navigator.share({
                    title: '电子签名成功',
                    text: shareText,
                    url: window.location.href
                }).catch(err => {
                    console.log('分享失败:', err);
                    fallbackShare(shareText);
                });
            } else {
                fallbackShare(shareText);
            }
        }

        // 备用分享方法
        function fallbackShare(text) {
            // 复制到剪贴板
            if (navigator.clipboard) {
                navigator.clipboard.writeText(text).then(() => {
                    showToast('签名信息已复制到剪贴板');
                }).catch(() => {
                    showShareModal(text);
                });
            } else {
                showShareModal(text);
            }
        }

        // 显示分享模态框
        function showShareModal(text) {
            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: rgba(0, 0, 0, 0.5);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 9999;
                padding: 20px;
            `;
            
            modal.innerHTML = `
                <div style="
                    background: white;
                    border-radius: 12px;
                    padding: 20px;
                    max-width: 400px;
                    width: 100%;
                ">
                    <h3 style="margin-bottom: 15px; color: #333;">分享签名信息</h3>
                    <textarea readonly style="
                        width: 100%;
                        height: 120px;
                        padding: 10px;
                        border: 1px solid #ddd;
                        border-radius: 6px;
                        resize: none;
                        font-size: 14px;
                        margin-bottom: 15px;
                    ">${text}</textarea>
                    <div style="display: flex; gap: 10px; justify-content: flex-end;">
                        <button onclick="this.closest('div').parentElement.remove()" style="
                            padding: 8px 16px;
                            border: 1px solid #ddd;
                            background: white;
                            border-radius: 6px;
                            cursor: pointer;
                        ">关闭</button>
                        <button onclick="copyText('${text.replace(/'/g, "\\'")}')"; this.closest('div').parentElement.remove();" style="
                            padding: 8px 16px;
                            border: none;
                            background: #667eea;
                            color: white;
                            border-radius: 6px;
                            cursor: pointer;
                        ">复制</button>
                    </div>
                </div>
            `;
            
            document.body.appendChild(modal);
        }

        // 复制文本
        function copyText(text) {
            const textarea = document.createElement('textarea');
            textarea.value = text;
            document.body.appendChild(textarea);
            textarea.select();
            document.execCommand('copy');
            document.body.removeChild(textarea);
            showToast('已复制到剪贴板');
        }

        // 显示提示消息
        function showToast(message) {
            const toast = document.createElement('div');
            toast.style.cssText = `
                position: fixed;
                top: 20px;
                left: 50%;
                transform: translateX(-50%);
                background: #4CAF50;
                color: white;
                padding: 12px 20px;
                border-radius: 6px;
                z-index: 10000;
                font-size: 14px;
                animation: slideDown 0.3s ease-out;
            `;
            toast.textContent = message;
            
            document.body.appendChild(toast);
            
            setTimeout(() => {
                toast.style.animation = 'slideUp 0.3s ease-out';
                setTimeout(() => {
                    document.body.removeChild(toast);
                }, 300);
            }, 2000);
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 添加键盘快捷键
            document.addEventListener('keydown', function(e) {
                // Ctrl/Cmd + P 预览文档
                if ((e.ctrlKey || e.metaKey) && e.key === 'p') {
                    e.preventDefault();
                    previewDocument();
                }
                
                // Ctrl/Cmd + S 分享
                if ((e.ctrlKey || e.metaKey) && e.key === 's') {
                    e.preventDefault();
                    shareSignature();
                }
            });
            
            // 添加CSS动画
            const style = document.createElement('style');
            style.textContent = `
                @keyframes slideDown {
                    from {
                        opacity: 0;
                        transform: translateX(-50%) translateY(-20px);
                    }
                    to {
                        opacity: 1;
                        transform: translateX(-50%) translateY(0);
                    }
                }
                
                @keyframes slideUp {
                    from {
                        opacity: 1;
                        transform: translateX(-50%) translateY(0);
                    }
                    to {
                        opacity: 0;
                        transform: translateX(-50%) translateY(-20px);
                    }
                }
            `;
            document.head.appendChild(style);
        });

        // 防止意外离开页面
        let hasInteracted = false;
        document.addEventListener('click', () => hasInteracted = true);
        
        window.addEventListener('beforeunload', function(e) {
            if (!hasInteracted) {
                e.preventDefault();
                e.returnValue = '确定要离开此页面吗？您的签名信息将无法再次查看。';
            }
        });
    </script>
</body>
</html>