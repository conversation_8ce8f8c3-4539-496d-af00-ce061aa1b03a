// 移动端签署JavaScript
let currentStep = 1;
let canvas, ctx;
let isDrawing = false;
let lastX = 0;
let lastY = 0;

document.addEventListener('DOMContentLoaded', function() {
    // 初始化
    initializeSignature();
    bindEvents();
    
    // 检查屏幕方向
    checkOrientation();
    window.addEventListener('orientationchange', checkOrientation);
});

function bindEvents() {
    // 下一步按钮
    const nextBtn = document.getElementById('nextBtn');
    if (nextBtn) {
        nextBtn.addEventListener('click', function() {
            const signerName = document.getElementById('signerName').value.trim();
            if (!signerName) {
                showMessage('请输入您的姓名', 'error');
                return;
            }
            goToStep(2);
        });
    }

    // 清除按钮
    const clearBtn = document.getElementById('clearBtn');
    if (clearBtn) {
        clearBtn.addEventListener('click', clearSignature);
    }

    // 提交按钮
    const submitBtn = document.getElementById('submitBtn');
    if (submitBtn) {
        submitBtn.addEventListener('click', submitSignature);
    }
}

function initializeSignature() {
    canvas = document.getElementById('signatureCanvas');
    if (!canvas) return;
    
    ctx = canvas.getContext('2d');
    
    // 设置画布样式
    ctx.strokeStyle = '#000';
    ctx.lineWidth = 2;
    ctx.lineCap = 'round';
    ctx.lineJoin = 'round';
    
    // 绑定绘制事件
    bindDrawingEvents();
}

function bindDrawingEvents() {
    // 鼠标事件
    canvas.addEventListener('mousedown', startDrawing);
    canvas.addEventListener('mousemove', draw);
    canvas.addEventListener('mouseup', stopDrawing);
    canvas.addEventListener('mouseout', stopDrawing);
    
    // 触摸事件
    canvas.addEventListener('touchstart', handleTouch);
    canvas.addEventListener('touchmove', handleTouch);
    canvas.addEventListener('touchend', stopDrawing);
    
    // 防止页面滚动
    canvas.addEventListener('touchstart', preventDefault);
    canvas.addEventListener('touchmove', preventDefault);
}

function startDrawing(e) {
    isDrawing = true;
    [lastX, lastY] = getCoordinates(e);
}

function draw(e) {
    if (!isDrawing) return;
    
    const [currentX, currentY] = getCoordinates(e);
    
    ctx.beginPath();
    ctx.moveTo(lastX, lastY);
    ctx.lineTo(currentX, currentY);
    ctx.stroke();
    
    [lastX, lastY] = [currentX, currentY];
}

function stopDrawing() {
    isDrawing = false;
}

function handleTouch(e) {
    e.preventDefault();
    const touch = e.touches[0];
    const mouseEvent = new MouseEvent(e.type === 'touchstart' ? 'mousedown' : 
                                     e.type === 'touchmove' ? 'mousemove' : 'mouseup', {
        clientX: touch.clientX,
        clientY: touch.clientY
    });
    canvas.dispatchEvent(mouseEvent);
}

function getCoordinates(e) {
    const rect = canvas.getBoundingClientRect();
    const scaleX = canvas.width / rect.width;
    const scaleY = canvas.height / rect.height;
    
    return [
        (e.clientX - rect.left) * scaleX,
        (e.clientY - rect.top) * scaleY
    ];
}

function preventDefault(e) {
    e.preventDefault();
}

function clearSignature() {
    ctx.clearRect(0, 0, canvas.width, canvas.height);
}

function goToStep(step) {
    // 隐藏所有步骤
    document.querySelectorAll('.step').forEach(el => {
        el.classList.remove('active');
    });
    
    // 显示目标步骤
    const targetStep = document.getElementById(getStepId(step));
    if (targetStep) {
        targetStep.classList.add('active');
        currentStep = step;
    }
}

function getStepId(step) {
    switch(step) {
        case 1: return 'nameStep';
        case 2: return 'signStep';
        case 3: return 'previewStep';
        default: return 'nameStep';
    }
}

async function submitSignature() {
    // 检查是否有签名
    if (isCanvasEmpty()) {
        showMessage('请先进行签名', 'error');
        return;
    }

    // 验证签名质量
    if (!validateSignature()) {
        showMessage('签名过于简单，请重新签名', 'warning');
        return;
    }

    const signerName = document.getElementById('signerName').value.trim();
    const signatureData = canvas.toDataURL('image/png').split(',')[1]; // 移除data:image/png;base64,前缀

    const requestData = {
        signer_name: signerName,
        signature_data: signatureData
    };

    try {
        showLoading(true);

        console.log('发送签名请求:', {
            url: `/api/sign/${window.templateID}`,
            data: requestData
        });

        const response = await fetch(`/api/sign/${window.templateID}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            },
            body: JSON.stringify(requestData),
            credentials: 'same-origin'
        });

        if (response.ok) {
            const result = await response.json();

            // 检查是否有警告
            if (result.warning) {
                showMessage('签署成功，但文档处理可能需要时间', 'warning');
            } else {
                showMessage('签署成功！', 'success');
            }

            // 显示预览步骤
            goToStep(3);
            showPreview(result);
        } else {
            console.error('签署请求失败:', response.status, response.statusText);
            try {
                const error = await response.json();
                showMessage('签署失败: ' + error.error, 'error');
            } catch (parseError) {
                showMessage(`签署失败: HTTP ${response.status} ${response.statusText}`, 'error');
            }
        }
    } catch (error) {
        console.error('网络请求错误:', error);
        if (error.name === 'TypeError' && error.message.includes('fetch')) {
            showMessage('网络连接失败，请检查网络连接或服务器状态', 'error');
        } else {
            showMessage('网络错误: ' + error.message, 'error');
        }
    } finally {
        showLoading(false);
    }
}

function isCanvasEmpty() {
    const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
    return imageData.data.every(pixel => pixel === 0);
}

function validateSignature() {
    // 简单的签名质量验证
    const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
    let pixelCount = 0;

    // 计算非透明像素数量
    for (let i = 3; i < imageData.data.length; i += 4) {
        if (imageData.data[i] > 0) {
            pixelCount++;
        }
    }

    // 签名应该至少有一定数量的像素
    const minPixels = 100; // 最少100个像素
    const maxPixels = canvas.width * canvas.height * 0.3; // 最多30%的画布

    return pixelCount >= minPixels && pixelCount <= maxPixels;
}

function showPreview(signatureResult) {
    const previewContainer = document.getElementById('documentPreview');
    if (previewContainer) {
        previewContainer.innerHTML = `
            <div class="preview-info">
                <h3>签署完成</h3>
                <p><strong>签署人:</strong> ${signatureResult.signature.signer_name}</p>
                <p><strong>签署时间:</strong> ${new Date(signatureResult.signature.signed_at).toLocaleString()}</p>
                <p><strong>文档ID:</strong> ${signatureResult.signature.id}</p>
                <div class="preview-actions">
                    <button class="btn btn-primary" onclick="previewDocument(${signatureResult.signature.id})">
                        预览签署后文档
                    </button>
                    <button class="btn btn-secondary" onclick="downloadDocument(${signatureResult.signature.id})">
                        下载文档
                    </button>
                </div>
                <div class="success-message">
                    ${signatureResult.message || '文档签署成功！'}
                </div>
            </div>
        `;
    }
}

async function previewDocument(signatureId) {
    try {
        const response = await fetch(`/api/preview/${signatureId}`);
        if (response.ok) {
            const result = await response.json();
            if (result.document_info && result.document_info.document_url) {
                // 在新窗口中打开PDF文档
                window.open(result.document_info.document_url, '_blank');
            } else {
                showMessage('文档预览链接不可用', 'error');
            }
        } else {
            const error = await response.json();
            showMessage('预览失败: ' + error.error, 'error');
        }
    } catch (error) {
        showMessage('网络错误: ' + error.message, 'error');
    }
}

function downloadDocument(signatureId) {
    // 直接下载文档
    window.location.href = `/api/download/${signatureId}`;
}

function checkOrientation() {
    const tip = document.querySelector('.landscape-tip');
    if (window.innerHeight > window.innerWidth) {
        // 竖屏状态
        if (!tip) {
            const tipEl = document.createElement('div');
            tipEl.className = 'landscape-tip';
            tipEl.textContent = '建议将手机横屏以获得更好的签名体验';
            document.body.insertBefore(tipEl, document.body.firstChild);
        }
    } else {
        // 横屏状态
        if (tip) {
            tip.remove();
        }
    }
}

function showLoading(show) {
    const submitBtn = document.getElementById('submitBtn');
    if (submitBtn) {
        if (show) {
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<span class="loading"></span> 提交中...';
        } else {
            submitBtn.disabled = false;
            submitBtn.innerHTML = '提交签名';
        }
    }
}

function showMessage(message, type = 'info') {
    // 移除现有消息
    const existingMessage = document.querySelector('.message');
    if (existingMessage) {
        existingMessage.remove();
    }
    
    // 创建消息元素
    const messageEl = document.createElement('div');
    messageEl.className = `message ${type}-message`;
    messageEl.textContent = message;
    
    // 添加到页面顶部
    document.body.insertBefore(messageEl, document.body.firstChild);
    
    // 3秒后自动移除
    setTimeout(() => {
        if (messageEl.parentNode) {
            messageEl.parentNode.removeChild(messageEl);
        }
    }, 3000);
}
