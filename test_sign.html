<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>电子签名测试</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        h1 {
            text-align: center;
            color: #2c3e50;
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: #333;
        }
        input {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 6px;
            font-size: 16px;
            box-sizing: border-box;
        }
        input:focus {
            outline: none;
            border-color: #3498db;
        }
        .signature-area {
            border: 2px dashed #ddd;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            margin: 20px 0;
        }
        canvas {
            border: 1px solid #ccc;
            border-radius: 4px;
            cursor: crosshair;
            max-width: 100%;
        }
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            margin: 5px;
            transition: all 0.3s;
        }
        .btn-primary {
            background-color: #3498db;
            color: white;
        }
        .btn-primary:hover {
            background-color: #2980b9;
        }
        .btn-secondary {
            background-color: #95a5a6;
            color: white;
        }
        .btn-secondary:hover {
            background-color: #7f8c8d;
        }
        .btn:disabled {
            background-color: #bdc3c7;
            cursor: not-allowed;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 6px;
            display: none;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .warning {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .actions {
            text-align: center;
            margin-top: 20px;
        }
        .signature-tips {
            background: #e8f4fd;
            border: 1px solid #bee5eb;
            border-radius: 4px;
            padding: 15px;
            margin: 15px 0;
            font-size: 14px;
            color: #0c5460;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>电子签名测试</h1>
        
        <form id="signForm">
            <div class="form-group">
                <label for="templateId">模板ID:</label>
                <input type="number" id="templateId" value="1" required>
            </div>
            
            <div class="form-group">
                <label for="signerName">签署人姓名:</label>
                <input type="text" id="signerName" placeholder="请输入您的姓名" required>
            </div>
            
            <div class="signature-tips">
                <strong>📝 签名说明：</strong>
                <ul style="margin: 10px 0; padding-left: 20px;">
                    <li>请在下方画布上用鼠标或触摸进行签名</li>
                    <li>签名不能过于简单，请认真书写</li>
                    <li>如需重新签名，请点击"清除"按钮</li>
                </ul>
            </div>
            
            <div class="signature-area">
                <label>手写签名:</label>
                <canvas id="signatureCanvas" width="600" height="200"></canvas>
                <div class="actions">
                    <button type="button" id="clearBtn" class="btn btn-secondary">清除签名</button>
                    <button type="submit" id="submitBtn" class="btn btn-primary">提交签名</button>
                </div>
            </div>
        </form>
        
        <div id="result" class="result"></div>
    </div>

    <script>
        let canvas, ctx;
        let isDrawing = false;
        let lastX = 0;
        let lastY = 0;

        document.addEventListener('DOMContentLoaded', function() {
            initializeCanvas();
            bindEvents();
        });

        function initializeCanvas() {
            canvas = document.getElementById('signatureCanvas');
            ctx = canvas.getContext('2d');
            
            ctx.strokeStyle = '#000';
            ctx.lineWidth = 2;
            ctx.lineCap = 'round';
            ctx.lineJoin = 'round';
            
            // 绑定绘制事件
            canvas.addEventListener('mousedown', startDrawing);
            canvas.addEventListener('mousemove', draw);
            canvas.addEventListener('mouseup', stopDrawing);
            canvas.addEventListener('mouseout', stopDrawing);
            
            // 触摸事件
            canvas.addEventListener('touchstart', handleTouch);
            canvas.addEventListener('touchmove', handleTouch);
            canvas.addEventListener('touchend', stopDrawing);
        }

        function bindEvents() {
            document.getElementById('clearBtn').addEventListener('click', clearSignature);
            document.getElementById('signForm').addEventListener('submit', submitSignature);
        }

        function startDrawing(e) {
            isDrawing = true;
            [lastX, lastY] = getCoordinates(e);
        }

        function draw(e) {
            if (!isDrawing) return;
            
            const [currentX, currentY] = getCoordinates(e);
            
            ctx.beginPath();
            ctx.moveTo(lastX, lastY);
            ctx.lineTo(currentX, currentY);
            ctx.stroke();
            
            [lastX, lastY] = [currentX, currentY];
        }

        function stopDrawing() {
            isDrawing = false;
        }

        function handleTouch(e) {
            e.preventDefault();
            const touch = e.touches[0];
            const mouseEvent = new MouseEvent(e.type === 'touchstart' ? 'mousedown' : 
                                             e.type === 'touchmove' ? 'mousemove' : 'mouseup', {
                clientX: touch.clientX,
                clientY: touch.clientY
            });
            canvas.dispatchEvent(mouseEvent);
        }

        function getCoordinates(e) {
            const rect = canvas.getBoundingClientRect();
            const scaleX = canvas.width / rect.width;
            const scaleY = canvas.height / rect.height;
            
            return [
                (e.clientX - rect.left) * scaleX,
                (e.clientY - rect.top) * scaleY
            ];
        }

        function clearSignature() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            hideResult();
        }

        function isCanvasEmpty() {
            const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
            return imageData.data.every(pixel => pixel === 0);
        }

        async function submitSignature(e) {
            e.preventDefault();
            
            if (isCanvasEmpty()) {
                showResult('请先进行签名', 'error');
                return;
            }
            
            const templateId = document.getElementById('templateId').value;
            const signerName = document.getElementById('signerName').value.trim();
            
            if (!signerName) {
                showResult('请输入签署人姓名', 'error');
                return;
            }
            
            const signatureData = canvas.toDataURL('image/png').split(',')[1];
            
            const requestData = {
                signer_name: signerName,
                signature_data: signatureData
            };
            
            try {
                setLoading(true);
                
                const response = await fetch(`http://localhost:8080/api/sign/${templateId}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(requestData)
                });
                
                const result = await response.json();
                
                if (response.ok) {
                    let message = '签署成功！';
                    let type = 'success';
                    
                    if (result.warning) {
                        message += `<br><strong>警告:</strong> ${result.warning}`;
                        type = 'warning';
                    }
                    
                    message += `<br><strong>签署ID:</strong> ${result.signature.id}`;
                    message += `<br><strong>签署时间:</strong> ${new Date(result.signature.signed_at).toLocaleString()}`;
                    message += `<br><br><button class="btn btn-primary" onclick="previewDocument(${result.signature.id})">预览文档</button>`;
                    
                    showResult(message, type);
                } else {
                    showResult('签署失败: ' + result.error, 'error');
                }
            } catch (error) {
                showResult('网络错误: ' + error.message, 'error');
            } finally {
                setLoading(false);
            }
        }

        async function previewDocument(signatureId) {
            try {
                const response = await fetch(`http://localhost:8080/api/preview/${signatureId}`);
                const result = await response.json();
                
                if (response.ok && result.document_info) {
                    window.open(result.document_info.document_url, '_blank');
                } else {
                    alert('预览失败: ' + (result.error || '未知错误'));
                }
            } catch (error) {
                alert('网络错误: ' + error.message);
            }
        }

        function setLoading(loading) {
            const submitBtn = document.getElementById('submitBtn');
            if (loading) {
                submitBtn.disabled = true;
                submitBtn.textContent = '提交中...';
            } else {
                submitBtn.disabled = false;
                submitBtn.textContent = '提交签名';
            }
        }

        function showResult(message, type) {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = message;
            resultDiv.className = `result ${type}`;
            resultDiv.style.display = 'block';
        }

        function hideResult() {
            const resultDiv = document.getElementById('result');
            resultDiv.style.display = 'none';
        }
    </script>
</body>
</html>
