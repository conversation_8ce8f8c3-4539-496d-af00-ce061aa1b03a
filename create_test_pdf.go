package main

import (
	"github.com/jung-kurt/gofpdf"
)

func main() {
	// 创建新的PDF文档
	pdf := gofpdf.New("P", "pt", "A4", "")
	pdf.AddPage()

	// 设置字体
	pdf.SetFont("Arial", "B", 16)
	pdf.Cell(0, 25, "Test Template Document")
	pdf.Ln(35)

	// 添加内容
	pdf.SetFont("Arial", "", 12)
	pdf.Cell(0, 15, "This is a test template for electronic signature system.")
	pdf.Ln(20)
	pdf.Cell(0, 15, "Please sign in the designated area below:")
	pdf.Ln(30)

	// 添加签名区域标识
	pdf.SetFont("Arial", "I", 10)
	pdf.Cell(0, 15, "Signature Area (400, 400):")
	pdf.Ln(20)
	
	// 绘制签名框
	pdf.Rect(400, 400, 200, 100, "D")
	pdf.SetXY(410, 420)
	pdf.Cell(0, 15, "Please sign here")

	// 保存PDF
	err := pdf.OutputFileAndClose("test_template.pdf")
	if err != nil {
		panic(err)
	}
	
	println("Test PDF created: test_template.pdf")
}
