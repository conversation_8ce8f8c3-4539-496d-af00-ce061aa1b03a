package service

import (
	"fmt"
	"path/filepath"

	"esign/internal/model"
	"esign/internal/repository"

	"github.com/skip2/go-qrcode"
)

// TemplateService 模板服务接口
type TemplateService interface {
	CreateTemplate(req *model.CreateTemplateRequest, filePath string) (*model.Template, error)
	GetTemplate(id uint) (*model.Template, error)
	GetAllTemplates() ([]*model.Template, error)
	UpdateTemplate(id uint, req *model.UpdateTemplateRequest) (*model.Template, error)
	DeleteTemplate(id uint) error
	GenerateQRCode(templateID uint, baseURL string) (string, error)
}

// templateService 模板服务实现
type templateService struct {
	templateRepo repository.TemplateRepository
}

// NewTemplateService 创建模板服务实例
func NewTemplateService(templateRepo repository.TemplateRepository) TemplateService {
	return &templateService{
		templateRepo: templateRepo,
	}
}

// CreateTemplate 创建模板
func (s *templateService) CreateTemplate(req *model.CreateTemplateRequest, filePath string) (*model.Template, error) {
	// 创建模板记录
	template := &repository.Template{
		Name:            req.Name,
		FilePath:        filePath,
		SignatureX:      req.SignatureX,
		SignatureY:      req.SignatureY,
		SignatureWidth:  req.SignatureWidth,
		SignatureHeight: req.SignatureHeight,
	}

	if template.SignatureWidth == 0 {
		template.SignatureWidth = 100
	}
	if template.SignatureHeight == 0 {
		template.SignatureHeight = 50
	}

	err := s.templateRepo.Create(template)
	if err != nil {
		return nil, err
	}

	// 转换为模型
	return s.convertToModel(template), nil
}

// GetTemplate 获取模板
func (s *templateService) GetTemplate(id uint) (*model.Template, error) {
	template, err := s.templateRepo.GetByID(id)
	if err != nil {
		return nil, err
	}
	return s.convertToModel(template), nil
}

// GetAllTemplates 获取所有模板
func (s *templateService) GetAllTemplates() ([]*model.Template, error) {
	templates, err := s.templateRepo.GetAll()
	if err != nil {
		return nil, err
	}

	var result []*model.Template
	for _, template := range templates {
		result = append(result, s.convertToModel(template))
	}
	return result, nil
}

// UpdateTemplate 更新模板
func (s *templateService) UpdateTemplate(id uint, req *model.UpdateTemplateRequest) (*model.Template, error) {
	template, err := s.templateRepo.GetByID(id)
	if err != nil {
		return nil, err
	}

	// 更新字段
	if req.Name != "" {
		template.Name = req.Name
	}
	if req.SignatureX != 0 {
		template.SignatureX = req.SignatureX
	}
	if req.SignatureY != 0 {
		template.SignatureY = req.SignatureY
	}
	if req.SignatureWidth != 0 {
		template.SignatureWidth = req.SignatureWidth
	}
	if req.SignatureHeight != 0 {
		template.SignatureHeight = req.SignatureHeight
	}

	err = s.templateRepo.Update(template)
	if err != nil {
		return nil, err
	}

	return s.convertToModel(template), nil
}

// DeleteTemplate 删除模板
func (s *templateService) DeleteTemplate(id uint) error {
	return s.templateRepo.Delete(id)
}

// GenerateQRCode 生成二维码
func (s *templateService) GenerateQRCode(templateID uint, baseURL string) (string, error) {
	// 生成签署链接
	signURL := fmt.Sprintf("%s/sign/%d", baseURL, templateID)
	
	// 生成二维码文件路径
	qrCodePath := filepath.Join("storage", "qrcodes", fmt.Sprintf("template_%d.png", templateID))
	
	// 生成二维码
	err := qrcode.WriteFile(signURL, qrcode.Medium, 256, qrCodePath)
	if err != nil {
		return "", err
	}

	// 更新模板的二维码路径
	template, err := s.templateRepo.GetByID(templateID)
	if err != nil {
		return "", err
	}
	
	template.QRCodePath = qrCodePath
	err = s.templateRepo.Update(template)
	if err != nil {
		return "", err
	}

	return qrCodePath, nil
}

// convertToModel 转换为模型
func (s *templateService) convertToModel(template *repository.Template) *model.Template {
	return &model.Template{
		ID:              template.ID,
		Name:            template.Name,
		FilePath:        template.FilePath,
		SignatureX:      template.SignatureX,
		SignatureY:      template.SignatureY,
		SignatureWidth:  template.SignatureWidth,
		SignatureHeight: template.SignatureHeight,
		QRCodePath:      template.QRCodePath,
		CreatedAt:       template.CreatedAt,
		UpdatedAt:       template.UpdatedAt,
	}
}
