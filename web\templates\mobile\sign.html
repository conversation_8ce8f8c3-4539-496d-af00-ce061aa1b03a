<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>{{.title}}</title>
    <link rel="stylesheet" href="/static/css/mobile.css">
</head>
<body>
    <div class="container">
        <header>
            <h1>电子签名</h1>
        </header>
        
        <main>
            <!-- 步骤1: 输入姓名 -->
            <section id="nameStep" class="step active">
                <h2>请输入您的姓名</h2>
                <div class="form-group">
                    <input type="text" id="signerName" placeholder="请输入您的姓名" required>
                </div>
                <button id="nextBtn" class="btn btn-primary">下一步</button>
            </section>
            
            <!-- 步骤2: 手写签名 -->
            <section id="signStep" class="step">
                <h2>请在下方签名</h2>
                <div class="signature-tips">
                    <p>📝 签名提示：</p>
                    <ul style="text-align: left; margin: 5px 0; padding-left: 20px;">
                        <li>请使用手指或触控笔在画布上签名</li>
                        <li>建议横屏操作以获得更好体验</li>
                        <li>签名不能过于简单，请认真书写</li>
                        <li>如需重新签名，请点击"清除"按钮</li>
                    </ul>
                </div>
                <div class="signature-container">
                    <canvas id="signatureCanvas" width="800" height="300"></canvas>
                    <div class="signature-actions">
                        <button id="clearBtn" class="btn btn-secondary">清除</button>
                        <button id="submitBtn" class="btn btn-primary">提交签名</button>
                    </div>
                </div>
                <p class="tip">请将手机横屏以获得更好的签名体验</p>
            </section>
            
            <!-- 步骤3: 预览文档 -->
            <section id="previewStep" class="step">
                <h2>签署完成</h2>
                <div class="preview-container">
                    <p>恭喜！您已成功完成签署。</p>
                    <div id="documentPreview">
                        <!-- 文档预览将在这里显示 -->
                    </div>
                </div>
            </section>
        </main>
    </div>

    <script>
        // 传递模板ID到JavaScript
        window.templateID = "{{.templateID}}";
        console.log('模板ID已设置:', window.templateID);

        // 添加全局错误处理
        window.addEventListener('error', function(e) {
            console.error('JavaScript错误:', e.error);
            console.error('错误位置:', e.filename, '行:', e.lineno);
        });

        // 检查fetch支持
        if (typeof fetch === 'undefined') {
            console.error('浏览器不支持fetch API');
        } else {
            console.log('浏览器支持fetch API');
        }
    </script>
    <script src="/static/js/mobile.js"></script>
</body>
</html>
