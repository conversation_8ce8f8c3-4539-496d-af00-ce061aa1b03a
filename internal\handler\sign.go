package handler

import (
	"fmt"
	"net/http"
	"os"
	"strconv"
	"strings"

	"esign/internal/model"
	"esign/internal/service"

	"github.com/gin-gonic/gin"
)

// SignHandler 签署处理器
type SignHandler struct {
	templateService  service.TemplateService
	signatureService service.SignatureService
	pdfService       service.PDFService
}

// NewSignHandler 创建签署处理器实例
func NewSignHandler(templateService service.TemplateService, signatureService service.SignatureService, pdfService service.PDFService) *SignHandler {
	return &SignHandler{
		templateService:  templateService,
		signatureService: signatureService,
		pdfService:       pdfService,
	}
}

// GetSignPage 获取签署页面信息
func (h *SignHandler) GetSignPage(c *gin.Context) {
	templateIDStr := c.Param("template_id")
	templateID, err := strconv.ParseUint(templateIDStr, 10, 32)
	if err != nil {
		c.<PERSON>(http.StatusBadRequest, gin.H{"error": "Invalid template ID"})
		return
	}

	template, err := h.templateService.GetTemplate(uint(templateID))
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Template not found"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"template": template,
		"message":  "Ready to sign",
	})
}

// SubmitSignature 提交签名
func (h *SignHandler) SubmitSignature(c *gin.Context) {
	templateIDStr := c.Param("template_id")
	templateID, err := strconv.ParseUint(templateIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid template ID"})
		return
	}

	var req model.CreateSignatureRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// 获取客户端信息
	ipAddress := c.ClientIP()
	userAgent := c.GetHeader("User-Agent")

	// 创建签署记录
	signature, err := h.signatureService.CreateSignature(uint(templateID), &req, ipAddress, userAgent)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// 处理签署后的PDF文档
	var processingError error
	if err := h.signatureService.ProcessSignedDocument(signature.ID); err != nil {
		processingError = err
	}

	// 检查请求类型，决定响应格式
	acceptHeader := c.GetHeader("Accept")
	isAPIRequest := strings.Contains(acceptHeader, "application/json") ||
		c.GetHeader("Content-Type") == "application/json"

	if isAPIRequest {
		// API请求，返回JSON响应
		if processingError != nil {
			c.JSON(http.StatusCreated, gin.H{
				"signature": signature,
				"warning":   "Document processing failed: " + processingError.Error(),
				"message":   "Signature recorded but document processing incomplete",
			})
		} else {
			c.JSON(http.StatusCreated, gin.H{
				"signature": signature,
				"message":   "Signature submitted successfully",
			})
		}
	} else {
		// 表单提交，跳转到成功页面
		c.Redirect(http.StatusSeeOther, fmt.Sprintf("/success/%d", signature.ID))
	}
}

// PreviewDocument 预览签署后的文档
func (h *SignHandler) PreviewDocument(c *gin.Context) {
	signatureIDStr := c.Param("signature_id")
	signatureID, err := strconv.ParseUint(signatureIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid signature ID"})
		return
	}

	signature, err := h.signatureService.GetSignature(uint(signatureID))
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Signature not found"})
		return
	}

	// 检查签署后文档是否存在
	documentPath := signature.SignedDocumentPath
	if _, err := os.Stat(documentPath); os.IsNotExist(err) {
		// 如果文档不存在，尝试重新生成
		if err := h.signatureService.ProcessSignedDocument(signature.ID); err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"error": "Document not found and regeneration failed: " + err.Error(),
			})
			return
		}
	}

	// 返回文档预览信息
	c.JSON(http.StatusOK, gin.H{
		"signature": signature,
		"document_info": gin.H{
			"signer_name":   signature.SignerName,
			"signed_at":     signature.SignedAt,
			"template_name": signature.Template.Name,
			"document_url":  "/" + strings.ReplaceAll(documentPath, "\\", "/"),
			"download_name": fmt.Sprintf("signed_%s_%s.pdf", signature.Template.Name, signature.SignerName),
		},
		"message": "Document ready for preview",
	})
}

// DownloadDocument 下载签署后的文档
func (h *SignHandler) DownloadDocument(c *gin.Context) {
	signatureIDStr := c.Param("signature_id")
	signatureID, err := strconv.ParseUint(signatureIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid signature ID"})
		return
	}

	signature, err := h.signatureService.GetSignature(uint(signatureID))
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Signature not found"})
		return
	}

	// 检查文档是否存在
	documentPath := signature.SignedDocumentPath
	if _, err := os.Stat(documentPath); os.IsNotExist(err) {
		c.JSON(http.StatusNotFound, gin.H{"error": "Document not found"})
		return
	}

	// 设置下载文件名
	filename := fmt.Sprintf("signed_%s_%s.pdf", signature.Template.Name, signature.SignerName)
	c.Header("Content-Disposition", fmt.Sprintf("attachment; filename=\"%s\"", filename))
	c.Header("Content-Type", "application/pdf")

	// 发送文件
	c.File(documentPath)
}
