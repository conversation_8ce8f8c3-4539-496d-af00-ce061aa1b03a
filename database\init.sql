-- 电子签名应用数据库初始化脚本
-- 创建时间: 2025-08-10

-- 模板表
CREATE TABLE IF NOT EXISTS templates (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    signature_x FLOAT NOT NULL,
    signature_y FLOAT NOT NULL,
    signature_width FLOAT DEFAULT 100,
    signature_height FLOAT DEFAULT 50,
    qr_code_path VARCHAR(500),
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 签署记录表
CREATE TABLE IF NOT EXISTS signatures (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    template_id INTEGER NOT NULL,
    signer_name VARCHAR(100) NOT NULL,
    signature_path VARCHAR(500) NOT NULL,
    signed_document_path VARCHAR(500) NOT NULL,
    ip_address VARCHAR(45),
    user_agent TEXT,
    signed_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIG<PERSON> KEY (template_id) REFERENCES templates(id) ON DELETE CASCADE
);

-- 创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_templates_name ON templates(name);
CREATE INDEX IF NOT EXISTS idx_templates_created_at ON templates(created_at);
CREATE INDEX IF NOT EXISTS idx_signatures_template_id ON signatures(template_id);
CREATE INDEX IF NOT EXISTS idx_signatures_signer_name ON signatures(signer_name);
CREATE INDEX IF NOT EXISTS idx_signatures_signed_at ON signatures(signed_at);

-- 插入示例数据（可选）
-- INSERT INTO templates (name, file_path, signature_x, signature_y) 
-- VALUES ('示例合同模板', 'storage/templates/sample.pdf', 100.0, 200.0);
